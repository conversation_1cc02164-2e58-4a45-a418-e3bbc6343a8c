import React, { useState, useEffect } from 'react';
import {
  Drawer,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Switch,
  message,
  Row,
  Col,
  Space,
  Typography,
  Button as AntButton,
} from 'antd';
import { useFetchClient } from '@strapi/helper-plugin';
import { Save, Edit } from 'lucide-react';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Text } = Typography;

interface PromotionModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  editingPromotion?: any;
}

interface Category {
  id: number;
  name: string;
}

interface Product {
  id: number;
  name: string;
  gia_ban: number;
}

const PromotionModal: React.FC<PromotionModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  editingPromotion,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const { get, post, put } = useFetchClient();

  const isEditing = !!editingPromotion;

  // Fetch categories and products
  useEffect(() => {
    if (visible) {
      fetchCategories();
      fetchProducts();
    }
  }, [visible]);

  // Set form values when editing
  useEffect(() => {
    if (visible && editingPromotion) {
      form.setFieldsValue({
        ...editingPromotion,
        dateRange: [dayjs(editingPromotion.startDate), dayjs(editingPromotion.endDate)],
        applicableProducts: editingPromotion.applicableProducts?.map((p: any) => p.id),
        applicableCategories: editingPromotion.applicableCategories?.map((c: any) => c.id),
      });
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingPromotion, form]);

  const fetchCategories = async () => {
    try {
      const response = await get('/management/products/categories');
      setCategories(response.data.data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await get('/management/products?pageSize=1000');
      setProducts(response.data.data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // Prepare data
      const promotionData = {
        ...values,
        startDate: values.dateRange[0].toISOString(),
        endDate: values.dateRange[1].toISOString(),
        applicableProducts: values.applicableProducts || [],
        applicableCategories: values.applicableCategories || [],
      };

      delete promotionData.dateRange;

      if (isEditing) {
        await put(`/management/promotions/${editingPromotion.id}`, {
          promotionData,
        });
        message.success('Cập nhật khuyến mãi thành công');
      } else {
        await post('/management/promotions', {
          promotionData,
        });
        message.success('Tạo khuyến mãi thành công');
      }

      onSuccess();
      form.resetFields();
    } catch (error: any) {
      console.error('Error saving promotion:', error);
      message.error(
        error.response?.data?.message || `Không thể ${isEditing ? 'cập nhật' : 'tạo'} khuyến mãi`
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const promotionTypes = [
    { value: 'percentage', label: 'Giảm theo phần trăm (%)' },
    { value: 'fixed_amount', label: 'Giảm số tiền cố định (VNĐ)' },
    { value: 'free_shipping', label: 'Miễn phí vận chuyển' },
  ];

  return (
    <Drawer
      title={
        <span
          style={{
            fontWeight: 500,
            fontSize: 16,
            color: '#344054',
            fontFamily: "'Be Vietnam Pro', sans-serif",
          }}
        >
          {isEditing ? 'Chỉnh sửa khuyến mãi' : 'Thêm khuyến mãi mới'}
        </span>
      }
      placement="right"
      width="70%"
      open={visible}
      onClose={handleCancel}
      footer={
        <div style={{ textAlign: 'right' }}>
          <AntButton style={{ marginRight: 8 }} disabled={loading} onClick={handleCancel}>
            Hủy bỏ
          </AntButton>
          <AntButton
            type="primary"
            icon={!isEditing ? <Save size={16} /> : <Edit size={16} />}
            onClick={handleSubmit}
            loading={loading}
            style={{ marginRight: 8 }}
          >
            {!isEditing ? 'Tạo' : 'Cập nhật'}
          </AntButton>
        </div>
      }
    >
      <div>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            isActive: true,
            isPublic: true,
            type: 'percentage',
            value: 0,
            minOrderAmount: 0,
            usageCount: 0,
          }}
          style={{
            fontFamily: "'Be Vietnam Pro', sans-serif",
          }}
        >
          {/* Thông tin cơ bản */}
          <Typography.Title level={5} style={{ marginBottom: 16, color: '#344054' }}>
            Thông tin cơ bản
          </Typography.Title>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Tên khuyến mãi"
                rules={[{ required: true, message: 'Vui lòng nhập tên khuyến mãi' }]}
              >
                <Input placeholder="Nhập tên khuyến mãi" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="Mã khuyến mãi"
                rules={[
                  { required: true, message: 'Vui lòng nhập mã khuyến mãi' },
                  {
                    min: 3,
                    max: 20,
                    message: 'Mã khuyến mãi phải từ 3 đến 20 ký tự',
                  },
                  {
                    pattern: /^[A-Z0-9]+$/,
                    message: 'Mã chỉ được chứa chữ hoa và số, không có ký tự đặc biệt',
                  },
                  {
                    validator: (_, value) => {
                      if (!value) {
                        return Promise.resolve();
                      }

                      // Check for common invalid patterns
                      if (/^[0-9]+$/.test(value)) {
                        return Promise.reject(
                          new Error('Mã khuyến mãi phải chứa ít nhất một chữ cái')
                        );
                      }

                      // Check for reserved words
                      const reservedWords = ['ADMIN', 'TEST', 'NULL', 'UNDEFINED'];
                      if (reservedWords.includes(value.toUpperCase())) {
                        return Promise.reject(
                          new Error('Mã khuyến mãi không được sử dụng từ khóa hệ thống')
                        );
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input placeholder="VD: SAVE10" style={{ textTransform: 'uppercase' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Mô tả">
            <TextArea rows={3} placeholder="Nhập mô tả khuyến mãi" />
          </Form.Item>

          {/* Cấu hình khuyến mãi */}
          <Typography.Title level={5} style={{ marginTop: 24, marginBottom: 16, color: '#344054' }}>
            Cấu hình khuyến mãi
          </Typography.Title>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="Loại khuyến mãi"
                rules={[{ required: true, message: 'Vui lòng chọn loại khuyến mãi' }]}
              >
                <Select placeholder="Chọn loại khuyến mãi">
                  {promotionTypes.map((type) => (
                    <Option key={type.value} value={type.value}>
                      {type.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="value"
                label="Giá trị"
                rules={[
                  { required: true, message: 'Vui lòng nhập giá trị' },
                  {
                    validator: (_, value) => {
                      const type = form.getFieldValue('type');

                      if (value === undefined || value === null || value === '') {
                        return Promise.resolve();
                      }

                      if (type === 'percentage') {
                        if (value <= 0 || value > 100) {
                          return Promise.reject(
                            new Error('Phần trăm giảm giá phải từ 1% đến 100%')
                          );
                        }
                      } else if (type === 'fixed_amount') {
                        if (value <= 0) {
                          return Promise.reject(new Error('Số tiền giảm phải lớn hơn 0'));
                        }
                        if (value > 10000000) {
                          return Promise.reject(
                            new Error('Số tiền giảm không được vượt quá 10,000,000 VNĐ')
                          );
                        }
                      } else if (type === 'free_shipping') {
                        // For free shipping, value should be 0
                        if (value !== 0) {
                          return Promise.reject(
                            new Error('Miễn phí vận chuyển không cần nhập giá trị')
                          );
                        }
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
                dependencies={['type']} // Re-validate when type changes
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="Nhập giá trị"
                  min={0}
                  formatter={(value: string | number | undefined): string => {
                    const type = form.getFieldValue('type');
                    if (type === 'percentage') {
                      return `${value}%`;
                    } else if (type === 'fixed_amount') {
                      return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                    }
                    return `${value}`;
                  }}
                  parser={(value: string | undefined): string => value!.replace(/[^\d]/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="minOrderAmount"
                label="Đơn hàng tối thiểu (VNĐ)"
                rules={[{ required: true, message: 'Vui lòng nhập đơn hàng tối thiểu' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  formatter={(value: string | number | undefined): string =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value: string | undefined): string => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="maxDiscountAmount"
                label="Giảm tối đa (VNĐ)"
                rules={[
                  {
                    validator: (_, value) => {
                      if (!value) {
                        return Promise.resolve(); // Optional field
                      }

                      const minOrderAmount = form.getFieldValue('minOrderAmount');
                      const type = form.getFieldValue('type');

                      if (value <= 0) {
                        return Promise.reject(new Error('Giảm tối đa phải lớn hơn 0'));
                      }

                      // For percentage type, check if max discount makes sense
                      if (type === 'percentage' && minOrderAmount) {
                        const maxPossibleDiscount = (minOrderAmount * 100) / 100; // 100% of min order
                        if (value > maxPossibleDiscount) {
                          return Promise.reject(
                            new Error(
                              `Giảm tối đa không nên vượt quá ${maxPossibleDiscount.toLocaleString('vi-VN')} VNĐ`
                            )
                          );
                        }
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
                dependencies={['minOrderAmount', 'type']}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="Không giới hạn"
                  min={0}
                  formatter={(value: string | number | undefined): string =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value: string | undefined): string => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          {/* Điều kiện và giới hạn */}
          <Typography.Title level={5} style={{ marginTop: 24, marginBottom: 16, color: '#344054' }}>
            Điều kiện và giới hạn
          </Typography.Title>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="usageLimit"
                label="Giới hạn sử dụng"
                rules={[
                  {
                    validator: (_, value) => {
                      if (!value) {
                        return Promise.resolve(); // Optional field
                      }

                      if (value <= 0) {
                        return Promise.reject(new Error('Giới hạn sử dụng phải lớn hơn 0'));
                      }

                      if (value > 1000000) {
                        return Promise.reject(
                          new Error('Giới hạn sử dụng không được vượt quá 1,000,000')
                        );
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="Không giới hạn"
                  min={1}
                  max={1000000}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="dateRange"
                label="Thời gian áp dụng"
                rules={[
                  { required: true, message: 'Vui lòng chọn thời gian áp dụng' },
                  {
                    validator: (_, value) => {
                      if (!value || !value[0] || !value[1]) {
                        return Promise.resolve();
                      }

                      const startDate = dayjs(value[0]);
                      const endDate = dayjs(value[1]);
                      const now = dayjs();

                      // Check if start date is in the past (only for new promotions)
                      if (!isEditing && startDate.isBefore(now, 'minute')) {
                        return Promise.reject(
                          new Error('Ngày bắt đầu không được là thời điểm trong quá khứ')
                        );
                      }

                      // Check if start date is after or equal to end date
                      if (startDate.isAfter(endDate) || startDate.isSame(endDate)) {
                        return Promise.reject(new Error('Ngày bắt đầu phải nhỏ hơn ngày kết thúc'));
                      }

                      // Check minimum duration (at least 1 hour)
                      if (endDate.diff(startDate, 'hour') < 1) {
                        return Promise.reject(new Error('Thời gian khuyến mãi phải ít nhất 1 giờ'));
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <RangePicker
                  style={{ width: '100%' }}
                  showTime
                  format="DD/MM/YYYY HH:mm"
                  placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
                />
              </Form.Item>
            </Col>
          </Row>

          {/* Phạm vi áp dụng */}
          <Typography.Title level={5} style={{ marginTop: 24, marginBottom: 16, color: '#344054' }}>
            Phạm vi áp dụng
          </Typography.Title>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="applicableCategories" label="Áp dụng cho danh mục">
                <Select mode="multiple" placeholder="Chọn danh mục (để trống = tất cả)" allowClear>
                  {categories.map((category) => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="applicableProducts" label="Áp dụng cho sản phẩm">
                <Select
                  mode="multiple"
                  placeholder="Chọn sản phẩm (để trống = tất cả)"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                >
                  {products.map((product) => (
                    <Option key={product.id} value={product.id}>
                      {product.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* Trạng thái */}
          <Typography.Title level={5} style={{ marginTop: 24, marginBottom: 16, color: '#344054' }}>
            Trạng thái
          </Typography.Title>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="isActive" label="Trạng thái" valuePropName="checked">
                <Switch checkedChildren="Hoạt động" unCheckedChildren="Tạm dừng" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="isPublic" label="Hiển thị công khai" valuePropName="checked">
                <Switch checkedChildren="Công khai" unCheckedChildren="Riêng tư" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </Drawer>
  );
};

export default PromotionModal;
