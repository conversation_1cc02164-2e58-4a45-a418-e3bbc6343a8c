import React, { useState, useEffect } from 'react';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Filter,
  Package,
  AlertCircle,
  CheckCircle2,
  ShoppingCart,
  Download,
  RefreshCw,
  HelpCircle,
  Save,
} from 'lucide-react';
import * as XLSX from 'xlsx';
import { useFetchClient } from '@strapi/helper-plugin';
import { useUpload } from '../hooks/useUpload';
import {
  Modal,
  message,
  Spin,
  Image,
  Table as AntTable,
  Empty,
  Button as AntButton,
  Row,
  Col,
  Drawer,
  Form,
  Input,
  Select,
  InputNumber,
  Tooltip,
  Space,
} from 'antd';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import {
  Page<PERSON>ontainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
  FilterGroup,
  FilterLabel,
  DateInput,
  SelectInput,
  ActionButtonGroup,
  ImageDisplay,
  StyledTable,
  QuickAddModal,
  SharedImageUpload,
  FileUpload,
} from './shared';

import { createGlobalStyle } from 'styled-components';

// Global styles for ReactQuill and Upload
const QuillStyles = createGlobalStyle`
  .ql-editor {
    min-height: 250px;
    font-size: 14px;
    line-height: 1.6;
    font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  .ql-toolbar {
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #d9d9d9;
    background: #fafafa;
  }

  .ql-container {
    border: none;
    font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  .ql-editor.ql-blank::before {
    color: #bfbfbf;
    font-style: normal;
    font-family: 'Be Vietnam Pro', sans-serif;
  }

  /* Ensure proper styling for product description display */
  .product-description {
    font-family: 'Be Vietnam Pro', sans-serif;
    line-height: 1.6;
  }

  .product-description h1,
  .product-description h2,
  .product-description h3 {
    margin-top: 1em;
    margin-bottom: 0.5em;
    font-weight: 600;
  }

  .product-description p {
    margin-bottom: 1em;
  }

  .product-description ul,
  .product-description ol {
    margin-bottom: 1em;
    padding-left: 1.5em;
  }

  .product-description a {
    color: #2563eb;
    text-decoration: underline;
  }

  /* Custom Upload Button Styling */
  .ant-upload.ant-upload-select.ant-upload-select-picture-card {
    width: 102px !important;
    height: 102px !important;
    margin-inline-end: 8px !important;
    margin-bottom: 8px !important;
    text-align: center !important;
    vertical-align: top !important;
    background-color: rgba(0, 0, 0, 0.02) !important;
    border: 1px dashed #d9d9d9 !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: border-color 0.3s !important;
  }

  .ant-upload.ant-upload-select.ant-upload-select-picture-card:hover {
    border-color: #1890ff !important;
  }

  .ant-upload.ant-upload-select.ant-upload-select-picture-card .ant-upload-text {
    font-size: 12px !important;
    color: #666 !important;
    margin-top: 8px !important;
  }
`;

// Styled Components (keeping only unique ones for ProductManagement)
import styled from 'styled-components';

const ProductInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

// ProductImageContainer now using shared ImageDisplay component

const ProductName = styled.div`
  display: flex;
  flex-direction: column;

  p {
    font-weight: 500;
    color: #2563eb;
    cursor: pointer;
    margin: 0;
    transition: color 0.2s ease;

    &:hover {
      color: #1d4ed8;
    }
  }
`;

const CategoryBadge = styled.span`
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
`;

const StockText = styled.span<{ $color: string }>`
  color: ${(props) => {
    switch (props.$color) {
      case 'text-red':
        return '#ef4444';
      case 'text-orange':
        return '#f59e0b';
      case 'text-green':
        return '#10b981';
      default:
        return '#374151';
    }
  }};
`;

const Switch = styled.label`
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
`;

const Slider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;

  &:before {
    position: absolute;
    content: '';
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }

  input:checked + & {
    background-color: #2563eb;
  }

  input:checked + &:before {
    transform: translateX(20px);
  }
`;

// ActionButtons now using shared ActionButtonGroup component

const PaginationContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
`;

const PaginationInfo = styled.div`
  font-size: 14px;
  color: #6b7280;
`;

const PaginationControls = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const PaginationButton = styled.button<{ $active?: boolean }>`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #ffffff;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  ${(props) =>
    props.$active &&
    `
    background: #2563eb;
    color: #ffffff;
    border-color: #2563eb;
  `}
`;

interface Product {
  id: string;
  name: string;
  sku?: string;
  gia_ban: number;
  gia_goc: number;
  discount: number;
  hinh_anh?: any;
  danh_muc?: {
    id: string;
    name: string;
  };
  thuong_hieu?: {
    id: string;
    name: string;
  };
  so_luong_ton_kho: number;
  da_ban?: number;
  luot_xem?: number;
  luot_yeu_thich?: number;
  mo_ta?: string;
  hot: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ProductStats {
  totalProducts: number;
  inStockProducts: number;
  outOfStockProducts: number;
  activeProducts: number;
}

const ProductManagement = () => {
  const { get, post, put, del } = useFetchClient();
  const { uploadMultipleFiles, uploadState } = useUpload();
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [stats, setStats] = useState<ProductStats>({
    totalProducts: 0,
    inStockProducts: 0,
    outOfStockProducts: 0,
    activeProducts: 0,
  });
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    pageCount: 0,
  });
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    status: '',
    dateFrom: '',
    dateTo: '',
  });
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);

  // Quill editor configuration for product description
  const quillModules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ color: [] }, { background: [] }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ align: [] }],
      ['link'],
      ['clean'],
    ],
  };

  const quillFormats = [
    'header',
    'bold',
    'italic',
    'underline',
    'strike',
    'color',
    'background',
    'list',
    'bullet',
    'align',
    'link',
  ];

  // Product detail modal states
  const [productDetailVisible, setProductDetailVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [productViewers, setProductViewers] = useState<any[]>([]);
  const [productFavorites, setProductFavorites] = useState<any[]>([]);

  // Product edit modal states
  const [editProductVisible, setEditProductVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [categories, setCategories] = useState<any[]>([]);
  const [brands, setBrands] = useState<any[]>([]);
  const [uploadedImages, setUploadedImages] = useState<any[]>([]);
  const [isAddMode, setIsAddMode] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    custom_collections: '',
    brand_id: '',
    purchase_price: 0,
    sales_price: 0,
    opening_stock: 0,
  });

  // Quick add category/brand modal states
  const [quickAddVisible, setQuickAddVisible] = useState(false);
  const [quickAddType, setQuickAddType] = useState<'category' | 'brand'>('category');

  // Import products modal states
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importFile, setImportFile] = useState<any[]>([]);
  const [importSubmitting, setImportSubmitting] = useState(false);

  // Fetch products from API
  const fetchProducts = async (resetFilters = false) => {
    try {
      setLoading(true);

      // Reset filters if requested
      if (resetFilters) {
        setFilters({
          search: '',
          category: '',
          status: '',
          dateFrom: '',
          dateTo: '',
        });
        setSearchTerm('');
        setPagination((prev) => ({ ...prev, page: 1 }));
      }

      const currentFilters = resetFilters
        ? {
            search: '',
            category: '',
            status: '',
            dateFrom: '',
            dateTo: '',
          }
        : filters;

      const queryParams = new URLSearchParams({
        page: resetFilters ? '1' : pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
        ...(currentFilters.search && { search: currentFilters.search }),
        ...(currentFilters.category && { category: currentFilters.category }),
        ...(currentFilters.status && { status: currentFilters.status }),
        ...(currentFilters.dateFrom && { dateFrom: currentFilters.dateFrom }),
        ...(currentFilters.dateTo && { dateTo: currentFilters.dateTo }),
      });

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 5000)
      );

      const response = (await Promise.race([
        get(`/management/products?${queryParams}`),
        timeoutPromise,
      ])) as any;

      if (response && response.data) {
        setProducts(response.data.data || []);
        setPagination((prev) => ({
          ...prev,
          total: response.data.meta?.pagination?.total || 0,
          pageCount: response.data.meta?.pagination?.pageCount || 0,
        }));
        setStats(response.data.meta?.stats || stats);
      } else {
        // No data received, use sample data
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when filters change
  useEffect(() => {
    // For development/demo purposes, you can uncomment the line below to use sample data directly
    // setSampleData();
    fetchProducts();
    // Also fetch categories and brands on mount
    fetchCategoriesAndBrands();
  }, [pagination.page, pagination.pageSize]);

  useEffect(() => {
    if (pagination.page === 1) {
      fetchProducts();
    } else {
      setPagination((prev) => ({ ...prev, page: 1 }));
    }
  }, [filters]);

  // Handle search
  useEffect(() => {
    setFilters((prev) => ({ ...prev, search: searchTerm }));
  }, [searchTerm]);

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.map((product) => product.id));
    } else {
      setSelectedProducts([]);
    }
  };

  // Clear selections when products change (e.g., pagination, filters)
  useEffect(() => {
    setSelectedProducts([]);
  }, [pagination.page, filters]);

  // Handle product status toggle
  const handleStatusToggle = async (productId: string, newStatus: boolean) => {
    try {
      await put(`/management/products/${productId}/status`, {
        isActive: newStatus,
      });
      message.success('Cập nhật trạng thái thành công');
      fetchProducts();
    } catch (error) {
      console.error('Error updating product status:', error);
      message.error('Không thể cập nhật trạng thái sản phẩm');
    }
  };

  // Handle product deletion
  const handleDelete = (productId: string) => {
    Modal.confirm({
      title: 'Xác nhận xóa sản phẩm',
      content: 'Bạn có chắc chắn muốn xóa sản phẩm này không?',
      okText: 'Xóa',
      cancelText: 'Hủy',
      okType: 'danger',
      onOk: async () => {
        try {
          await del(`/management/products/${productId}`);
          message.success('Xóa sản phẩm thành công');
          fetchProducts();
        } catch (error) {
          console.error('Error deleting product:', error);
          message.error('Không thể xóa sản phẩm');
        }
      },
    });
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedProducts.length === 0) {
      message.warning('Vui lòng chọn ít nhất một sản phẩm để xóa');
      return;
    }

    Modal.confirm({
      title: 'Xác nhận xóa sản phẩm',
      content: `Bạn có chắc chắn muốn xóa ${selectedProducts.length} sản phẩm đã chọn không?`,
      okText: 'Xóa',
      cancelText: 'Hủy',
      okType: 'danger',
      onOk: async () => {
        try {
          // Delete products in parallel
          await Promise.all(
            selectedProducts.map((productId) => del(`/management/products/${productId}`))
          );
          message.success(`Xóa thành công ${selectedProducts.length} sản phẩm`);
          setSelectedProducts([]);
          fetchProducts();
        } catch (error) {
          console.error('Error deleting products:', error);
          message.error('Không thể xóa một số sản phẩm');
        }
      },
    });
  };

  // Product detail handlers
  const handleProductClick = async (product: Product) => {
    setSelectedProduct(product);
    try {
      // Fetch product viewers and favorites data
      // Note: These endpoints might not exist yet, so we'll handle gracefully
      const [viewersResponse, favoritesResponse] = await Promise.allSettled([
        get(`/management/products/${product.id}/viewers`),
        get(`/management/products/${product.id}/favorites`),
      ]);

      if (viewersResponse.status === 'fulfilled') {
        setProductViewers(viewersResponse.value.data || []);
      } else {
        setProductViewers([]);
      }

      if (favoritesResponse.status === 'fulfilled') {
        setProductFavorites(favoritesResponse.value.data || []);
      } else {
        setProductFavorites([]);
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
      setProductViewers([]);
      setProductFavorites([]);
    }
    setProductDetailVisible(true);
  };

  // Product edit handlers
  const handleEditProduct = async (product: Product) => {
    setEditingProduct(product);
    setIsAddMode(false);

    // Fetch categories and brands for the form
    await fetchCategoriesAndBrands();

    // Set form data state
    setFormData({
      name: product.name || '',
      description: product.mo_ta || '',
      custom_collections: product.danh_muc?.id || '',
      brand_id: product.thuong_hieu?.id || '',
      purchase_price: product.gia_goc || 0,
      sales_price: product.gia_ban || 0,
      opening_stock: product.so_luong_ton_kho || 0,
    });

    // Set uploaded images
    if (product.hinh_anh && product.hinh_anh.length > 0) {
      setUploadedImages(
        product.hinh_anh.map((img: any, index: number) => ({
          uid: img.id || index,
          name: img.name || `image-${index}`,
          status: 'done',
          url: img.url,
        }))
      );
    } else {
      setUploadedImages([]);
    }

    setEditProductVisible(true);
  };

  const handleEditSubmit = async () => {
    try {
      setSubmitting(true);

      // Validate required fields
      if (!formData.name || !formData.sales_price || !formData.purchase_price) {
        message.error('Vui lòng điền đầy đủ thông tin bắt buộc');
        setSubmitting(false);
        return;
      }

      if (uploadedImages.length === 0) {
        message.error('Vui lòng tải lên ít nhất một hình ảnh');
        setSubmitting(false);
        return;
      }

      // Prepare data for API
      const productData = {
        name: formData.name,
        gia_ban: parseFloat(formData.sales_price.toString()) || 0,
        gia_goc: parseFloat(formData.purchase_price.toString()) || 0,
        so_luong_ton_kho: parseInt(formData.opening_stock.toString()) || 0,
        mo_ta: formData.description || '',
        danh_muc: formData.custom_collections || null,
        thuong_hieu: formData.brand_id || null,
        hot: false,
        isActive: true,
      };

      if (isAddMode) {
        // For creating new product, handle image upload with retry mechanism
        const imageIds: number[] = [];
        const filesToUpload = uploadedImages.filter((file) => file.originFileObj);

        if (filesToUpload.length > 0) {
          try {
            const files = filesToUpload.map((file) => file.originFileObj as File);
            const { results, successCount } = await uploadMultipleFiles(files);

            if (successCount === 0) {
              message.error('Không thể tải lên hình ảnh nào. Vui lòng thử lại.');
              setSubmitting(false);
              return;
            }

            if (successCount < files.length) {
              message.warning(`Chỉ tải lên được ${successCount}/${files.length} hình ảnh.`);
            }

            // Collect successful upload IDs
            results.forEach((result) => {
              if (result.success && result.data && result.data.length > 0) {
                imageIds.push(result.data[0].id);
              }
            });
          } catch (uploadError) {
            console.error('Error uploading images:', uploadError);
            message.error('Không thể tải lên hình ảnh. Vui lòng thử lại.');
            setSubmitting(false);
            return;
          }
        }

        // Create product with image IDs
        const finalData = {
          ...productData,
          hinh_anh: imageIds,
        };

        await post('/management/products', { data: finalData });
        message.success('Thêm sản phẩm thành công');
      } else {
        // For updating existing product
        const finalData: any = { ...productData };

        // Handle image updates with retry mechanism
        const imageIds: number[] = [];
        const filesToUpload = uploadedImages.filter((file) => file.originFileObj);

        if (filesToUpload.length > 0) {
          try {
            const files = filesToUpload.map((file) => file.originFileObj as File);
            const { results, successCount } = await uploadMultipleFiles(files);

            if (successCount === 0) {
              message.error('Không thể tải lên hình ảnh nào. Vui lòng thử lại.');
              setSubmitting(false);
              return;
            }

            if (successCount < files.length) {
              message.warning(`Chỉ tải lên được ${successCount}/${files.length} hình ảnh.`);
            }

            // Collect successful upload IDs
            results.forEach((result) => {
              if (result.success && result.data && result.data.length > 0) {
                imageIds.push(result.data[0].id);
              }
            });
          } catch (uploadError) {
            console.error('Error uploading images:', uploadError);
            message.error('Không thể tải lên hình ảnh. Vui lòng thử lại.');
            setSubmitting(false);
            return;
          }
        }

        // Keep existing images
        uploadedImages.forEach((file) => {
          if (file.url && !file.originFileObj) {
            const existingImage = editingProduct?.hinh_anh?.find(
              (img: any) => img.url === file.url
            );
            if (existingImage) {
              imageIds.push(existingImage.id);
            }
          }
        });

        if (imageIds.length > 0) {
          finalData.hinh_anh = imageIds;
        }

        await put(`/management/products/${editingProduct?.id}`, { data: finalData });
        message.success('Cập nhật sản phẩm thành công');
      }

      setEditProductVisible(false);
      resetForm();
      setUploadedImages([]);
      setIsAddMode(false);
      fetchProducts();
    } catch (error) {
      console.error('Error saving product:', error);
      message.error(isAddMode ? 'Không thể thêm sản phẩm' : 'Không thể cập nhật sản phẩm');
    } finally {
      setSubmitting(false);
    }
  };

  // Reset form helper function
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      custom_collections: '',
      brand_id: '',
      purchase_price: 0,
      sales_price: 0,
      opening_stock: 0,
    });
  };

  // Handle add new product
  const handleAddProduct = async () => {
    setIsAddMode(true);
    setEditingProduct(null);

    // Fetch categories and brands for the form
    await fetchCategoriesAndBrands();

    // Reset form and images
    resetForm();
    setUploadedImages([]);
    setEditProductVisible(true);
  };

  // Fetch categories and brands - reusable function (only active ones)
  const fetchCategoriesAndBrands = async () => {
    try {
      const [categoriesResponse, brandsResponse] = await Promise.allSettled([
        get('/management/products/categories?activeOnly=true'),
        get('/management/products/brands?activeOnly=true'),
      ]);

      if (categoriesResponse.status === 'fulfilled') {
        const categoriesData = categoriesResponse.value.data || [];
        // Handle both direct array and nested data structure
        const categoryList = Array.isArray(categoriesData)
          ? categoriesData
          : categoriesData.data || [];

        // Backend already filters active categories
        setCategories(categoryList);

        if (categoryList.length === 0) {
          message.warning(
            'Chưa có danh mục sản phẩm hoạt động nào. Vui lòng tạo hoặc kích hoạt danh mục trước.'
          );
        }
      } else {
        console.error('Categories fetch failed:', categoriesResponse.reason);
        message.error('Không thể tải danh mục sản phẩm');
        setCategories([]);
      }

      if (brandsResponse.status === 'fulfilled') {
        const brandsData = brandsResponse.value.data || [];
        // Handle both direct array and nested data structure
        const brandList = Array.isArray(brandsData) ? brandsData : brandsData.data || [];

        // Backend already filters active brands
        setBrands(brandList);

        if (brandList.length === 0) {
          message.warning(
            'Chưa có thương hiệu hoạt động nào. Vui lòng tạo hoặc kích hoạt thương hiệu trước.'
          );
        }
      } else {
        console.error('Brands fetch failed:', brandsResponse.reason);
        message.error('Không thể tải thương hiệu');
        setBrands([]);
      }
    } catch (error) {
      console.error('Error fetching categories and brands:', error);
      message.error('Lỗi khi tải danh mục và thương hiệu');
      setCategories([]);
      setBrands([]);
    }
  };

  // Handle quick add category/brand
  const handleQuickAdd = (type: 'category' | 'brand') => {
    setQuickAddType(type);
    setQuickAddVisible(true);
  };

  // Handle import products
  const handleImportProducts = () => {
    setImportFile([]);
    setImportModalVisible(true);
  };

  // Handle download sample file
  const handleDownloadSample = async () => {
    try {
      // Use window.open to download file with authentication
      const downloadUrl = `/management/products/sample?timestamp=${Date.now()}`;
      window.open(downloadUrl, '_blank');

      message.success('Đang tải xuống file mẫu...');
    } catch (error) {
      console.error('Error downloading sample file:', error);
      message.error('Không thể tải xuống file mẫu');
    }
  };

  const handleImportSubmit = async () => {
    try {
      if (importFile.length === 0) {
        message.error('Vui lòng chọn tệp để nhập');
        return;
      }

      setImportSubmitting(true);

      const file = importFile[0];
      const formData = new FormData();
      formData.append('file', file.originFileObj || file);

      const response = await post('/management/products/import', formData);

      if (response.data?.success) {
        message.success(`Nhập thành công ${response.data.imported || 0} sản phẩm`);
        setImportModalVisible(false);
        setImportFile([]);
        fetchProducts();
      } else {
        message.error(response.data?.message || 'Có lỗi xảy ra khi nhập sản phẩm');
      }
    } catch (error) {
      console.error('Error importing products:', error);
      message.error('Không thể nhập sản phẩm');
    } finally {
      setImportSubmitting(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN').format(price);
  };

  // Helper function to format currency for modal
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount);
  };

  const getStockStatus = (stock: number) => {
    if (stock === 0) return { text: 'Hết hàng', color: 'text-red' };
    if (stock < 100) return { text: 'Sắp hết', color: 'text-orange' };
    return { text: 'Còn hàng', color: 'text-green' };
  };

  // Helper function to get product image URL
  const getProductImageUrl = (product: Product): string => {
    if (product.hinh_anh && product.hinh_anh.length > 0) {
      const firstImage = product.hinh_anh[0];
      // Prefer thumbnail for better performance, fallback to small, then original
      return (
        firstImage?.formats?.thumbnail?.url ||
        firstImage?.formats?.small?.url ||
        firstImage?.url ||
        '/placeholder.svg'
      );
    }
    return '/placeholder.svg';
  };

  // Handle export
  const handleExport = async () => {
    try {
      const queryParams = new URLSearchParams({
        format: 'excel',
        ...(filters.search && { 'filters[search]': filters.search }),
        ...(filters.category && { 'filters[category]': filters.category }),
        ...(filters.status && { 'filters[status]': filters.status }),
        ...(filters.dateFrom && { 'filters[dateFrom]': filters.dateFrom }),
        ...(filters.dateTo && { 'filters[dateTo]': filters.dateTo }),
      });

      const response = await get(`/management/products/export?${queryParams}`);

      if (response.data?.data) {
        // Convert to Excel and download
        createAndDownloadExcel(response.data.data);
        message.success('Xuất dữ liệu thành công');
      }
    } catch (error) {
      console.error('Error exporting products:', error);
      message.error('Không thể xuất dữ liệu');
    }
  };

  // Helper functions
  const createAndDownloadExcel = (data: any[]) => {
    if (!data.length) {
      message.warning('Không có dữ liệu để xuất');
      return;
    }

    // Define Vietnamese headers for better readability
    const headerMapping: { [key: string]: string } = {
      name: 'Tên sản phẩm',
      sku: 'Mã SKU',
      gia_ban: 'Giá bán (VNĐ)',
      gia_goc: 'Giá gốc (VNĐ)',
      so_luong_ton_kho: 'Tồn kho',
      'danh_muc.name': 'Danh mục',
      'thuong_hieu.name': 'Thương hiệu',
      luot_xem: 'Lượt xem',
      luot_yeu_thich: 'Lượt yêu thích',
      da_ban: 'Đã bán',
      hot: 'Sản phẩm hot',
      isActive: 'Trạng thái',
      createdAt: 'Ngày tạo',
      updatedAt: 'Ngày cập nhật',
    };

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Prepare data for Excel
    const excelData = data.map((product, index) => {
      const row: any = {
        STT: index + 1,
      };

      // Map all available fields
      Object.keys(product).forEach((key) => {
        const vietnameseHeader = headerMapping[key] || key;
        let value = product[key];

        // Format specific fields
        if (key === 'gia_ban' || key === 'gia_goc') {
          value = typeof value === 'number' ? value : 0; // Keep as number for Excel
        } else if (key === 'hot' || key === 'isActive') {
          value = value ? 'Có' : 'Không';
        } else if (key === 'createdAt' || key === 'updatedAt') {
          value = value ? new Date(value).toLocaleDateString('vi-VN') : '';
        } else if (key === 'danh_muc' && value && typeof value === 'object') {
          value = value.name || '';
        } else if (key === 'thuong_hieu' && value && typeof value === 'object') {
          value = value.name || '';
        }

        row[vietnameseHeader] = value || '';
      });

      return row;
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths
    const columnWidths = [
      { wch: 5 }, // STT
      { wch: 30 }, // Tên sản phẩm
      { wch: 15 }, // Mã SKU
      { wch: 15 }, // Giá bán
      { wch: 15 }, // Giá gốc
      { wch: 10 }, // Tồn kho
      { wch: 20 }, // Danh mục
      { wch: 20 }, // Thương hiệu
      { wch: 12 }, // Lượt xem
      { wch: 15 }, // Lượt yêu thích
      { wch: 10 }, // Đã bán
      { wch: 15 }, // Sản phẩm hot
      { wch: 12 }, // Trạng thái
      { wch: 18 }, // Ngày tạo
      { wch: 18 }, // Ngày cập nhật
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Danh sách sản phẩm');

    // Generate filename with current date
    const currentDate = new Date().toLocaleDateString('vi-VN').replace(/\//g, '-');
    const filename = `danh-sach-san-pham-${currentDate}.xlsx`;

    // Write and download file
    XLSX.writeFile(workbook, filename);
  };

  const statsData = [
    {
      title: 'Tổng sản phẩm',
      value: stats.totalProducts.toString(),
      icon: Package,
      color: 'bg-blue' as const,
    },
    {
      title: 'Sản phẩm còn hàng',
      value: stats.inStockProducts.toString(),
      icon: CheckCircle2,
      color: 'bg-blue-dark' as const,
    },
    {
      title: 'Sản phẩm hết hàng',
      value: stats.outOfStockProducts.toString(),
      icon: AlertCircle,
      color: 'bg-green' as const,
    },
    {
      title: 'Tổng sản phẩm đã bán',
      value: '0',
      icon: ShoppingCart,
      color: 'bg-red' as const,
    },
  ];

  // Define columns for Antd Table
  const columns = [
    {
      title: 'Sản phẩm',
      key: 'product',
      width: 300,
      render: (_: any, product: Product) => (
        <ProductInfo>
          <ImageDisplay
            src={getProductImageUrl(product)}
            alt={product.name}
            size={40}
            placeholder="Không có"
            preview={true}
            previewSrc={
              product.hinh_anh && product.hinh_anh.length > 0
                ? product.hinh_anh[0]?.formats?.large?.url ||
                  product.hinh_anh[0]?.formats?.medium?.url ||
                  product.hinh_anh[0]?.url ||
                  getProductImageUrl(product)
                : getProductImageUrl(product)
            }
          />
          <ProductName>
            <p onClick={() => handleProductClick(product)}>{product.name}</p>
          </ProductName>
        </ProductInfo>
      ),
      sorter: (a: Product, b: Product) => a.name.localeCompare(b.name),
    },
    {
      title: 'Giá bán',
      dataIndex: 'gia_ban',
      key: 'gia_ban',
      width: 120,
      render: (value: number) => formatPrice(value) || '-',
      sorter: (a: Product, b: Product) => (a.gia_ban || 0) - (b.gia_ban || 0),
    },
    {
      title: 'Giá gốc',
      dataIndex: 'gia_goc',
      key: 'gia_goc',
      width: 120,
      render: (value: number) => formatPrice(value) || '-',
      sorter: (a: Product, b: Product) => (a.gia_goc || 0) - (b.gia_goc || 0),
    },
    {
      title: 'Danh mục',
      key: 'category',
      width: 150,
      render: (_: any, product: Product) => (
        <CategoryBadge>{product.danh_muc?.name || '-'}</CategoryBadge>
      ),
      sorter: (a: Product, b: Product) =>
        (a.danh_muc?.name || '').localeCompare(b.danh_muc?.name || ''),
    },
    {
      title: 'Thương hiệu',
      key: 'brand',
      width: 150,
      render: (_: any, product: Product) => product.thuong_hieu?.name || '-',
      sorter: (a: Product, b: Product) =>
        (a.thuong_hieu?.name || '').localeCompare(b.thuong_hieu?.name || ''),
    },
    {
      title: 'Tồn kho',
      dataIndex: 'so_luong_ton_kho',
      key: 'so_luong_ton_kho',
      width: 100,
      render: (value: number) => {
        const stockStatus = getStockStatus(value);
        return <StockText $color={stockStatus.color}>{formatPrice(value)}</StockText>;
      },
      sorter: (a: Product, b: Product) => (a.so_luong_ton_kho || 0) - (b.so_luong_ton_kho || 0),
    },
    {
      title: 'Lượt xem',
      dataIndex: 'luot_xem',
      key: 'luot_xem',
      width: 100,
      render: (value: number) => `${formatPrice(value || 0)} lượt`,
      sorter: (a: Product, b: Product) => (a.luot_xem || 0) - (b.luot_xem || 0),
    },
    {
      title: 'Lượt yêu thích',
      dataIndex: 'luot_yeu_thich',
      key: 'luot_yeu_thich',
      width: 120,
      render: (value: number) => formatPrice(value || 0),
      sorter: (a: Product, b: Product) => (a.luot_yeu_thich || 0) - (b.luot_yeu_thich || 0),
    },
    {
      title: 'Hoạt động',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (value: boolean, product: Product) => (
        <Switch>
          <input
            type="checkbox"
            checked={value}
            onChange={(e) => handleStatusToggle(product.id, e.target.checked)}
          />
          <Slider />
        </Switch>
      ),
      sorter: (a: Product, b: Product) => Number(a.isActive) - Number(b.isActive),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a: Product, b: Product) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (_: any, product: Product) => (
        <ActionButtonGroup
          onView={() => handleProductClick(product)}
          onEdit={() => handleEditProduct(product)}
          onDelete={() => handleDelete(product.id)}
          deleteConfirmTitle="Xác nhận xóa"
          deleteConfirmDescription="Bạn có chắc chắn muốn xóa sản phẩm này?"
          showView={true}
          showEdit={true}
          showDelete={true}
          viewTooltip="Xem chi tiết sản phẩm"
          editTooltip="Chỉnh sửa sản phẩm"
          deleteTooltip="Xóa sản phẩm"
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <QuillStyles />
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* Product Management */}
        <Card>
          <PageHeader
            title="Danh sách sản phẩm"
            description={
              selectedProducts.length > 0
                ? `Đã chọn ${selectedProducts.length} sản phẩm`
                : 'Xem và quản lý danh sách sản phẩm'
            }
            actions={
              <>
                {selectedProducts.length > 0 && (
                  <Button
                    $variant="outline"
                    onClick={handleBulkDelete}
                    style={{ color: '#ef4444', borderColor: '#ef4444' }}
                  >
                    <Trash2 />
                    Xóa đã chọn ({selectedProducts.length})
                  </Button>
                )}
                <Button $variant="outline" onClick={handleExport}>
                  <Download />
                  Xuất Excel
                </Button>
                <Button $variant="outline" onClick={() => fetchProducts(true)} disabled={loading}>
                  <RefreshCw className={loading ? 'animate-spin' : ''} />
                  Làm mới
                </Button>
                <Button $variant="outline" onClick={handleImportProducts}>
                  <Filter />
                  Nhập sản phẩm
                </Button>
                <Button $variant="primary" onClick={handleAddProduct}>
                  <Plus />
                  Thêm mới sản phẩm
                </Button>
              </>
            }
          />

          <CardContent>
            {/* Search and Filters */}
            <FiltersSection>
              <SearchBar
                placeholder="Tìm kiếm sản phẩm..."
                value={searchTerm}
                onChange={setSearchTerm}
              />
              <FilterGroup>
                <FilterLabel>Ngày tạo từ:</FilterLabel>
                <DateInput
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => setFilters((prev) => ({ ...prev, dateFrom: e.target.value }))}
                />
              </FilterGroup>
              <FilterGroup>
                <FilterLabel>Ngày tạo đến:</FilterLabel>
                <DateInput
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => setFilters((prev) => ({ ...prev, dateTo: e.target.value }))}
                />
              </FilterGroup>
              <FilterGroup>
                <FilterLabel>Chọn trạng thái:</FilterLabel>
                <SelectInput
                  value={filters.status}
                  onChange={(e) => setFilters((prev) => ({ ...prev, status: e.target.value }))}
                >
                  <option value="">Tất cả</option>
                  <option value="true">Hoạt động</option>
                  <option value="false">Không hoạt động</option>
                </SelectInput>
              </FilterGroup>
            </FiltersSection>

            {/* Products Table */}
            <StyledTable>
              <AntTable
                columns={columns}
                dataSource={products}
                rowKey="id"
                loading={loading}
                pagination={false}
                scroll={{ x: 1200 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span
                          style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}
                        >
                          Không có dữ liệu
                        </span>
                      }
                    />
                  ),
                }}
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
                rowSelection={{
                  selectedRowKeys: selectedProducts,
                  onChange: (selectedRowKeys) => {
                    setSelectedProducts(selectedRowKeys as string[]);
                  },
                  onSelectAll: (selected) => {
                    handleSelectAll(selected);
                  },
                }}
              />
            </StyledTable>

            {/* Pagination */}
            <PaginationContainer>
              <PaginationInfo>
                Hiển thị {(pagination.page - 1) * pagination.pageSize + 1} đến{' '}
                {Math.min(pagination.page * pagination.pageSize, pagination.total)} của{' '}
                {pagination.total} kết quả
              </PaginationInfo>
              <PaginationControls>
                <PaginationButton
                  disabled={pagination.page <= 1 || loading}
                  onClick={() => setPagination((prev) => ({ ...prev, page: prev.page - 1 }))}
                >
                  Trước
                </PaginationButton>
                <PaginationButton $active>{pagination.page}</PaginationButton>
                <PaginationButton
                  disabled={pagination.page >= pagination.pageCount || loading}
                  onClick={() => setPagination((prev) => ({ ...prev, page: prev.page + 1 }))}
                >
                  Sau
                </PaginationButton>
              </PaginationControls>
            </PaginationContainer>
          </CardContent>
        </Card>
      </Spin>

      {/* Product Detail Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 20 }}>
            <span style={{ fontWeight: 500, fontSize: 16, color: '#344054' }}>
              {selectedProduct?.name || 'Chi tiết sản phẩm'}
            </span>
          </div>
        }
        open={productDetailVisible}
        onCancel={() => setProductDetailVisible(false)}
        footer={[
          <AntButton key="cancel" onClick={() => setProductDetailVisible(false)}>
            Hủy bỏ
          </AntButton>,
        ]}
        width={1200}
        style={{ top: 0, paddingBottom: 0 }}
        styles={{
          header: {
            borderBottom: '1px solid #e2e8f0',
            paddingBottom: '16px',
            marginBottom: '0',
            marginLeft: '-24px',
            marginRight: '-24px',
            paddingLeft: '24px',
            paddingRight: '24px',
          },
          body: {
            paddingTop: '24px',
          },
        }}
      >
        {selectedProduct && (
          <div className="user-details">
            <Row gutter={[16, 16]} style={{ marginLeft: -8, marginRight: -8, rowGap: 16 }}>
              {/* Product Images */}
              <Col xs={24} sm={24} md={3} lg={24} style={{ paddingLeft: 8, paddingRight: 8 }}>
                <span style={{ fontWeight: 500, fontSize: 15, color: '#344054' }}>Hình ảnh</span>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: 10, marginTop: 10 }}>
                  {selectedProduct.hinh_anh && selectedProduct.hinh_anh.length > 0 ? (
                    selectedProduct.hinh_anh.map((img: any, index: number) => (
                      <Image
                        key={index}
                        alt={`Image ${index + 1}`}
                        src={img.url}
                        style={{
                          width: 135,
                          height: 135,
                          objectFit: 'cover',
                          borderRadius: 5,
                        }}
                        preview={{
                          mask: (
                            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                              <Eye size={16} />
                              Preview
                            </div>
                          ),
                        }}
                      />
                    ))
                  ) : (
                    <div style={{ color: '#64748b', fontSize: 14 }}>Không có dữ liệu</div>
                  )}
                </div>
              </Col>

              {/* Product Details */}
              <Col
                xs={24}
                sm={24}
                md={24}
                lg={24}
                style={{ paddingLeft: 8, paddingRight: 8, width: '100%' }}
              >
                <div style={{ width: '100%' }}>
                  <div style={{ marginBottom: 16 }}>
                    <span style={{ fontWeight: 500, fontSize: 15, color: '#344054' }}>
                      Chi tiết sản phẩm
                    </span>
                  </div>
                  <div
                    style={{
                      border: '1px solid #e2e8f0',
                      borderRadius: '8px',
                      overflow: 'hidden',
                    }}
                  >
                    <table
                      style={{
                        width: '100%',
                        borderCollapse: 'collapse',
                        fontSize: 14,
                      }}
                    >
                      <tbody>
                        <tr style={{ borderBottom: '1px solid #e2e8f0' }}>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '30%',
                            }}
                          >
                            Tên
                          </th>
                          <td style={{ padding: '12px 16px', color: '#1e293b', fontSize: 14 }}>
                            {selectedProduct.name || 'N/A'}
                          </td>
                        </tr>

                        <tr style={{ borderBottom: '1px solid #e2e8f0' }}>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Danh mục
                          </th>
                          <td
                            style={{
                              padding: '12px 16px',
                              color: '#1e293b',
                              width: '25%',
                              fontSize: 14,
                            }}
                          >
                            {selectedProduct.danh_muc?.name || '-'}
                          </td>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Thương hiệu
                          </th>
                          <td
                            style={{
                              padding: '12px 16px',
                              color: '#1e293b',
                              width: '25%',
                              fontSize: 14,
                            }}
                          >
                            {selectedProduct.thuong_hieu?.name || '-'}
                          </td>
                        </tr>

                        <tr style={{ borderBottom: '1px solid #e2e8f0' }}>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Mã hàng hóa
                          </th>
                          <td style={{ padding: '12px 16px', color: '#1e293b', width: '25%' }}>
                            {selectedProduct.sku || selectedProduct.id || 'N/A'}
                          </td>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Tồn kho hiện tại
                          </th>
                          <td style={{ padding: '12px 16px', color: '#1e293b', width: '25%' }}>
                            <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                              <span
                                style={{
                                  width: 8,
                                  height: 8,
                                  borderRadius: '50%',
                                  backgroundColor:
                                    (selectedProduct.so_luong_ton_kho || 0) > 0
                                      ? '#10b981'
                                      : '#ef4444',
                                }}
                              />
                              {selectedProduct.so_luong_ton_kho || 0}
                            </span>
                          </td>
                        </tr>
                        <tr style={{ borderBottom: '1px solid #e2e8f0' }}>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Giá bán
                          </th>
                          <td style={{ padding: '12px 16px', color: '#1e293b', width: '25%' }}>
                            {formatCurrency(selectedProduct.gia_ban || 0)}
                          </td>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Giá gốc
                          </th>
                          <td style={{ padding: '12px 16px', color: '#1e293b', width: '25%' }}>
                            {formatCurrency(selectedProduct.gia_goc || 0)}
                          </td>
                        </tr>
                        <tr style={{ borderBottom: '1px solid #e2e8f0' }}>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Lượt xem
                          </th>
                          <td style={{ padding: '12px 16px', color: '#1e293b', width: '25%' }}>
                            {selectedProduct.luot_xem || 0}
                          </td>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Lượt yêu thích
                          </th>
                          <td style={{ padding: '12px 16px', color: '#1e293b', width: '25%' }}>
                            {selectedProduct.luot_yeu_thich || 0}
                          </td>
                        </tr>
                        <tr style={{ borderBottom: '1px solid #e2e8f0' }}>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Đã bán
                          </th>
                          <td style={{ padding: '12px 16px', color: '#1e293b', width: '25%' }}>
                            {selectedProduct.da_ban || 0}
                          </td>
                          <th
                            style={{
                              padding: '12px 16px',
                              textAlign: 'left',
                              fontWeight: 600,
                              color: '#64748b',
                              backgroundColor: '#f8fafc',
                              width: '25%',
                            }}
                          >
                            Trạng thái
                          </th>
                          <td style={{ padding: '12px 16px', color: '#1e293b', width: '25%' }}>
                            <span
                              style={{
                                padding: '4px 8px',
                                borderRadius: '4px',
                                backgroundColor: selectedProduct.isActive ? '#dcfce7' : '#fee2e2',
                                color: selectedProduct.isActive ? '#166534' : '#dc2626',
                                fontSize: '12px',
                                fontWeight: 500,
                              }}
                            >
                              {selectedProduct.isActive ? 'Hoạt động' : 'Không hoạt động'}
                            </span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </Col>
            </Row>

            {/* Product Description */}
            {selectedProduct.mo_ta && (
              <div style={{ marginTop: 20 }}>
                <div style={{ marginBottom: 16 }}>
                  <span style={{ fontWeight: 500, fontSize: 15, color: '#344054' }}>
                    Mô tả sản phẩm
                  </span>
                </div>
                <div
                  className="product-description"
                  style={{
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    padding: '16px',
                    backgroundColor: '#f8fafc',
                    fontSize: '14px',
                  }}
                  dangerouslySetInnerHTML={{ __html: selectedProduct.mo_ta }}
                />
              </div>
            )}

            {/* Product Viewers Table */}
            <div style={{ marginTop: 20 }}>
              <div style={{ marginBottom: 16 }}>
                <p style={{ fontSize: 15, fontWeight: 600 }}>Danh sách người xem sản phẩm</p>
              </div>
              <AntTable
                dataSource={productViewers}
                rowKey="id"
                pagination={false}
                size="middle"
                columns={[
                  {
                    title: 'Tên',
                    dataIndex: 'name',
                    key: 'name',
                    width: 150,
                  },
                  {
                    title: 'Tên',
                    dataIndex: 'displayName',
                    key: 'displayName',
                    width: 150,
                  },
                  {
                    title: 'Số điện thoại',
                    dataIndex: 'phone',
                    key: 'phone',
                    width: 130,
                  },
                  {
                    title: 'Thời gian xem',
                    dataIndex: 'viewedAt',
                    key: 'viewedAt',
                    width: 150,
                    render: (date: string) =>
                      date ? new Date(date).toLocaleString('vi-VN') : 'N/A',
                  },
                ]}
                locale={{
                  emptyText: (
                    <Empty description="Không có dữ liệu" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  ),
                }}
              />
            </div>

            {/* Product Favorites Table */}
            <div style={{ marginTop: 20 }}>
              <div style={{ marginBottom: 16 }}>
                <p style={{ fontSize: 15, fontWeight: 600 }}>Danh sách người yêu thích sản phẩm</p>
              </div>
              <AntTable
                dataSource={productFavorites}
                rowKey="id"
                pagination={false}
                size="middle"
                columns={[
                  {
                    title: 'Tên',
                    dataIndex: 'name',
                    key: 'name',
                    width: 150,
                  },
                  {
                    title: 'Tên',
                    dataIndex: 'displayName',
                    key: 'displayName',
                    width: 150,
                  },
                  {
                    title: 'Số điện thoại',
                    dataIndex: 'phone',
                    key: 'phone',
                    width: 130,
                  },
                  {
                    title: 'Thời gian xem',
                    dataIndex: 'favoriteAt',
                    key: 'favoriteAt',
                    width: 150,
                    render: (date: string) =>
                      date ? new Date(date).toLocaleString('vi-VN') : 'N/A',
                  },
                ]}
                locale={{
                  emptyText: (
                    <Empty description="Không có dữ liệu" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  ),
                }}
              />
            </div>
          </div>
        )}
      </Modal>

      {/* Product Edit Drawer */}
      <Drawer
        title={
          <span
            style={{
              fontWeight: 500,
              fontSize: 14,
              color: 'rgb(52, 64, 84)',
              fontFamily: 'Be Vietnam Pro, sans-serif',
            }}
          >
            {isAddMode ? 'Thêm sản phẩm' : 'Sửa sản phẩm'}
          </span>
        }
        placement="right"
        width="80%"
        open={editProductVisible}
        onClose={() => {
          setEditProductVisible(false);
          resetForm();
          setUploadedImages([]);
          setIsAddMode(false);
        }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <AntButton
              style={{ marginRight: 8 }}
              disabled={submitting}
              onClick={() => {
                setEditProductVisible(false);
                resetForm();
                setUploadedImages([]);
                setIsAddMode(false);
              }}
            >
              Hủy bỏ
            </AntButton>
            <AntButton
              type="primary"
              icon={isAddMode ? <Save size={16} /> : <Edit size={16} />}
              onClick={handleEditSubmit}
              loading={submitting}
              style={{ marginRight: 8 }}
            >
              {isAddMode ? 'Tạo' : 'Cập nhật'}
            </AntButton>
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #f0f0f0',
          },
        }}
      >
        <div style={{ fontFamily: 'Be Vietnam Pro, sans-serif' }}>
          {/* Image Upload */}
          <div style={{ marginBottom: 24 }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
              Hình ảnh
              <Tooltip
                title={
                  <div>
                    <div>Dung lượng tối đa: 2MB</div>
                    <div>Cho phép file định dạng sau: jpg, jpeg, png</div>
                  </div>
                }
                placement="top"
              >
                <span style={{ color: 'rgb(72, 72, 71)', cursor: 'help' }}>
                  <HelpCircle size={14} />
                </span>
              </Tooltip>
            </label>
            <Form.Item>
              <SharedImageUpload
                value={uploadedImages}
                onChange={setUploadedImages}
                maxCount={10}
                accept="image/*"
                uploadText="Tải lên"
              />
            </Form.Item>
          </div>

          {/* Name */}
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 8 }}>
              Tên <span style={{ color: 'red' }}>*</span>
            </label>
            <input
              type="text"
              placeholder="Vui lòng nhập Tên"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              style={{
                width: '100%',
                height: 40,
                borderRadius: 8,
                border: '1px solid #d9d9d9',
                padding: '0 12px',
                fontSize: 14,
                outline: 'none',
              }}
            />
          </div>

          {/* Category and Brand in same row */}
          <div style={{ display: 'flex', gap: 16, marginBottom: 16 }}>
            <div style={{ flex: 1 }}>
              <label style={{ display: 'block', marginBottom: 8 }}>
                Danh mục <span style={{ color: 'red' }}>*</span>
              </label>
              <div style={{ display: 'flex', gap: 8 }}>
                <Select
                  placeholder={
                    categories.length === 0 ? 'Đang tải danh mục...' : 'Chọn Danh mục...'
                  }
                  style={{ flex: 1, height: 40 }}
                  showSearch
                  allowClear
                  loading={categories.length === 0}
                  value={formData.custom_collections}
                  onChange={(value) => setFormData({ ...formData, custom_collections: value })}
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)
                      ?.toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  notFoundContent={
                    categories.length === 0
                      ? 'Chưa có danh mục. Hãy thêm danh mục mới.'
                      : 'Không tìm thấy'
                  }
                  onFocus={() => {
                    if (categories.length === 0) {
                      fetchCategoriesAndBrands();
                    }
                  }}
                >
                  {categories.map((cat) => (
                    <Select.Option key={cat.id} value={cat.id}>
                      {cat.name}
                    </Select.Option>
                  ))}
                </Select>
                <AntButton
                  icon={<Plus size={16} />}
                  onClick={() => handleQuickAdd('category')}
                  style={{
                    height: 40,
                    width: 40,
                    borderRadius: 8,
                    background: 'rgb(245, 245, 245)',
                    border: 'none',
                  }}
                />
              </div>
            </div>
            <div style={{ flex: 1 }}>
              <label style={{ display: 'block', marginBottom: 8 }}>
                Thương hiệu <span style={{ color: 'red' }}>*</span>
              </label>
              <div style={{ display: 'flex', gap: 8 }}>
                <Select
                  placeholder={
                    brands.length === 0 ? 'Đang tải thương hiệu...' : 'Chọn Thương hiệu...'
                  }
                  style={{ flex: 1, height: 40 }}
                  showSearch
                  allowClear
                  loading={brands.length === 0}
                  value={formData.brand_id}
                  onChange={(value) => setFormData({ ...formData, brand_id: value })}
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)
                      ?.toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  notFoundContent={
                    brands.length === 0
                      ? 'Chưa có thương hiệu. Hãy thêm thương hiệu mới.'
                      : 'Không tìm thấy'
                  }
                  onFocus={() => {
                    if (brands.length === 0) {
                      fetchCategoriesAndBrands();
                    }
                  }}
                >
                  {brands.map((brand) => (
                    <Select.Option key={brand.id} value={brand.id}>
                      {brand.name}
                    </Select.Option>
                  ))}
                </Select>
                <AntButton
                  icon={<Plus size={16} />}
                  onClick={() => handleQuickAdd('brand')}
                  style={{
                    height: 40,
                    width: 40,
                    borderRadius: 8,
                    background: 'rgb(245, 245, 245)',
                    border: 'none',
                  }}
                />
              </div>
            </div>
          </div>

          {/* Purchase Price and Sales Price in same row */}
          <div style={{ display: 'flex', gap: 16, marginBottom: 16 }}>
            <div style={{ flex: 1 }}>
              <label style={{ display: 'block', marginBottom: 8 }}>
                Giá gốc <span style={{ color: 'red' }}>*</span>
              </label>
              <input
                type="number"
                placeholder="Vui lòng nhập Giá gốc"
                value={formData.purchase_price}
                onChange={(e) =>
                  setFormData({ ...formData, purchase_price: Number(e.target.value) })
                }
                style={{
                  width: '100%',
                  height: 40,
                  borderRadius: 8,
                  border: '1px solid #d9d9d9',
                  padding: '0 12px',
                  fontSize: 14,
                  outline: 'none',
                }}
              />
            </div>
            <div style={{ flex: 1 }}>
              <label style={{ display: 'block', marginBottom: 8 }}>
                Giá bán <span style={{ color: 'red' }}>*</span>
              </label>
              <input
                type="number"
                placeholder="Vui lòng nhập Giá bán"
                value={formData.sales_price}
                onChange={(e) => setFormData({ ...formData, sales_price: Number(e.target.value) })}
                style={{
                  width: '100%',
                  height: 40,
                  borderRadius: 8,
                  border: '1px solid #d9d9d9',
                  padding: '0 12px',
                  fontSize: 14,
                  outline: 'none',
                }}
              />
            </div>
          </div>

          {/* Opening Stock */}
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 8 }}>Tồn kho</label>
            <input
              type="number"
              placeholder="0"
              value={formData.opening_stock}
              onChange={(e) => setFormData({ ...formData, opening_stock: Number(e.target.value) })}
              style={{
                width: '100%',
                height: 40,
                borderRadius: 8,
                border: '1px solid #d9d9d9',
                padding: '0 12px',
                fontSize: 14,
                outline: 'none',
              }}
            />
          </div>
          {/* Description */}
          <div style={{ marginBottom: 80 }}>
            <label style={{ display: 'block', marginBottom: 8 }}>Mô tả</label>
            <div
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: 8,
                overflow: 'hidden',
                backgroundColor: '#fff',
              }}
            >
              <ReactQuill
                theme="snow"
                value={formData.description}
                onChange={(content) => setFormData({ ...formData, description: content })}
                modules={quillModules}
                formats={quillFormats}
                placeholder="Vui lòng nhập mô tả sản phẩm..."
                style={{
                  height: '800px',
                  fontFamily: 'Be Vietnam Pro, sans-serif',
                }}
              />
            </div>
          </div>
        </div>
      </Drawer>

      {/* Quick Add Category/Brand Modal */}
      <QuickAddModal
        visible={quickAddVisible}
        onCancel={() => setQuickAddVisible(false)}
        type={quickAddType}
        onSuccess={fetchCategoriesAndBrands}
        showActiveSwitch={false}
        autoActive={true}
      />

      {/* Import Products Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 20 }}>
            <span style={{ fontWeight: 500, fontSize: 16, color: '#344054' }}>Nhập sản phẩm</span>
          </div>
        }
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={[
          <AntButton key="cancel" onClick={() => setImportModalVisible(false)}>
            Hủy bỏ
          </AntButton>,
          <AntButton
            key="submit"
            type="primary"
            loading={importSubmitting}
            onClick={handleImportSubmit}
          >
            Nhập vào
          </AntButton>,
        ]}
        width={600}
        style={{ top: 0, paddingBottom: 0 }}
        styles={{
          header: {
            borderBottom: '1px solid #e2e8f0',
            paddingBottom: '16px',
            marginBottom: '0',
            marginLeft: '-24px',
            marginRight: '-24px',
            paddingLeft: '24px',
            paddingRight: '24px',
          },
          body: {
            paddingTop: '24px',
          },
        }}
      >
        <Row gutter={[16, 16]} style={{ marginLeft: -8, marginRight: -8 }}>
          <Col xs={24} sm={24} md={24} lg={24} style={{ paddingLeft: 8, paddingRight: 8 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 10, marginTop: 6 }}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#0061F3"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M12 17V3"></path>
                <path d="m6 11 6 6 6-6"></path>
                <path d="M19 21H5"></path>
              </svg>
              <button
                onClick={handleDownloadSample}
                style={{
                  color: '#0061F3',
                  textDecoration: 'none',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  padding: 0,
                  font: 'inherit',
                }}
              >
                Tải xuống tệp Excel mẫu
              </button>
            </div>
          </Col>
        </Row>

        <Row style={{ marginLeft: -8, marginRight: -8, marginTop: 16 }}>
          <Col xs={24} sm={24} md={24} lg={24} style={{ paddingLeft: 8, paddingRight: 8 }}>
            <Form layout="vertical">
              <Form.Item
                label={
                  <span style={{ color: '#344054', fontSize: 14, fontWeight: 400 }}>Tệp tin</span>
                }
              >
                <FileUpload
                  value={importFile}
                  onChange={setImportFile}
                  accept=".xlsx,.csv"
                  maxCount={1}
                  buttonText="Tải lên file Excel/CSV"
                  beforeUpload={(file) => {
                    const isExcel =
                      file.type ===
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'text/csv' ||
                      file.name.endsWith('.xlsx') ||
                      file.name.endsWith('.csv');
                    if (!isExcel) {
                      message.error('Chỉ được tải lên file Excel (.xlsx) hoặc CSV!');
                      return false;
                    }
                    return false; // Prevent auto upload
                  }}
                />
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </Modal>
    </PageContainer>
  );
};

export default ProductManagement;
