import React, { useState } from 'react';
import { Modal, Form, Input, message, Space, Button, Switch } from 'antd';
import { useFetchClient } from '@strapi/helper-plugin';
import type { UploadFile } from 'antd/es/upload/interface';
import SharedImageUpload from './SharedImageUpload';

interface QuickAddModalProps {
  visible: boolean;
  onCancel: () => void;
  type: 'category' | 'brand';
  onSuccess?: () => void;
  showActiveSwitch?: boolean; // Hiển thị switch trạng thái
  autoActive?: boolean; // Tự động set active = true
  title?: string;
}

const QuickAddModal: React.FC<QuickAddModalProps> = ({
  visible,
  onCancel,
  type,
  onSuccess,
  showActiveSwitch = false,
  autoActive = true,
  title,
}) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const { post } = useFetchClient();

  const handleSubmit = async (values: any) => {
    if (submitting) return;

    setSubmitting(true);
    try {
      // Step 1: Upload file to media library first (if exists)
      let mediaId = null;
      if (fileList.length > 0 && fileList[0].originFileObj) {
        const uploadFormData = new FormData();
        uploadFormData.append('files', fileList[0].originFileObj);

        try {
          const uploadResponse = await post('/upload', uploadFormData);
          if (uploadResponse.data && uploadResponse.data.length > 0) {
            mediaId = uploadResponse.data[0].id;
          }
        } catch (uploadError) {
          console.error('Error uploading file:', uploadError);
          message.error('Không thể tải lên hình ảnh');
          setSubmitting(false);
          return;
        }
      }

      // Step 2: Create category/brand with media ID
      const isActiveValue = showActiveSwitch
        ? values.isActive !== undefined
          ? values.isActive
          : true
        : autoActive;

      const requestData: any = {
        name: values.name,
        isActive: isActiveValue,
      };

      // Add media reference
      if (mediaId) {
        requestData[type === 'category' ? 'image' : 'logo'] = mediaId;
      }

      if (type === 'category') {
        await post('/management/products/categories', { data: requestData });
        message.success('Tạo danh mục thành công');
      } else {
        // Add brand-specific fields
        requestData.description = values.description || '';
        requestData.website = values.website || '';

        await post('/management/products/brands', { data: requestData });
        message.success('Tạo thương hiệu thành công');
      }

      handleCancel();
      onSuccess?.();
    } catch (error: any) {
      console.error('Error creating:', error);
      message.error(
        error.response?.data?.message ||
          `Có lỗi xảy ra khi tạo ${type === 'category' ? 'danh mục' : 'thương hiệu'}`
      );
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setFileList([]);
    onCancel();
  };

  const modalTitle = title || (type === 'category' ? 'Thêm danh mục mới' : 'Thêm thương hiệu mới');

  return (
    <Modal title={modalTitle} open={visible} onCancel={handleCancel} footer={null} width={600}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        style={{ marginTop: 24 }}
        initialValues={{ isActive: true }}
      >
        <Form.Item
          label={type === 'category' ? 'Tên danh mục' : 'Tên thương hiệu'}
          name="name"
          rules={[
            {
              required: true,
              message: `Vui lòng nhập tên ${type === 'category' ? 'danh mục' : 'thương hiệu'}`,
            },
            {
              min: 2,
              message: `Tên ${type === 'category' ? 'danh mục' : 'thương hiệu'} phải có ít nhất 2 ký tự`,
            },
          ]}
        >
          <Input
            placeholder={`Nhập tên ${type === 'category' ? 'danh mục' : 'thương hiệu'}`}
            size="large"
          />
        </Form.Item>

        {type === 'brand' && (
          <>
            <Form.Item label="Mô tả" name="description">
              <Input.TextArea placeholder="Nhập mô tả thương hiệu" rows={3} size="large" />
            </Form.Item>

            <Form.Item label="Website" name="website">
              <Input placeholder="https://example.com" size="large" type="url" />
            </Form.Item>
          </>
        )}

        <Form.Item label={type === 'category' ? 'Hình ảnh' : 'Logo'}>
          <SharedImageUpload
            value={fileList}
            onChange={setFileList}
            maxCount={1}
            accept="image/*"
            uploadText="Tải lên"
          />
        </Form.Item>

        {showActiveSwitch && (
          <Form.Item label="Trạng thái" name="isActive" valuePropName="checked">
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Tạm dừng" />
          </Form.Item>
        )}

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel} disabled={submitting}>
              Hủy
            </Button>
            <Button type="primary" htmlType="submit" loading={submitting} disabled={submitting}>
              {type === 'category' ? 'Tạo danh mục' : 'Tạo thương hiệu'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default QuickAddModal;
