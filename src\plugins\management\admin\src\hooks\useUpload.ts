import { useState } from 'react';
import { message } from 'antd';
import { useFetchClient } from '@strapi/helper-plugin';

interface UploadOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
  onProgress?: (progress: number) => void;
}

interface UploadResult {
  success: boolean;
  data?: any;
  error?: string;
}

interface UploadState {
  uploading: boolean;
  progress: number;
}

/**
 * Custom hook for file upload with retry mechanism
 */
export const useUpload = () => {
  const { post } = useFetchClient();
  const [uploadState, setUploadState] = useState<UploadState>({
    uploading: false,
    progress: 0,
  });

  /**
   * Validate file before upload
   */
  const validateFile = (file: File): { valid: boolean; error?: string } => {
    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File quá lớn. <PERSON>í<PERSON> thước tối đa là 10MB.',
      };
    }

    // Check file type for images
    if (file.type.startsWith('image/')) {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        return {
          valid: false,
          error: 'Định dạng file không được hỗ trợ. Chỉ chấp nhận: JPG, PNG, GIF, WebP.',
        };
      }
    }

    return { valid: true };
  };

  /**
   * Upload single file with retry mechanism
   */
  const uploadFile = async (file: File, options: UploadOptions = {}): Promise<UploadResult> => {
    const { maxRetries = 3, retryDelay = 1000, onProgress } = options;

    setUploadState({ uploading: true, progress: 0 });

    let lastError: any = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Validate file before upload
        const validationResult = validateFile(file);
        if (!validationResult.valid) {
          setUploadState({ uploading: false, progress: 0 });
          return {
            success: false,
            error: validationResult.error,
          };
        }

        // Create FormData
        const formData = new FormData();
        formData.append('files', file);

        // Update progress
        const progressValue = (attempt - 1) * 30; // Show some progress during retries
        setUploadState({ uploading: true, progress: progressValue });
        onProgress?.(progressValue);

        // Upload using Strapi's authenticated client
        const response = await post('/upload', formData);

        if (response.data && response.data.length > 0) {
          setUploadState({ uploading: false, progress: 100 });
          onProgress?.(100);
          return {
            success: true,
            data: response.data,
          };
        } else {
          throw new Error('No data returned from upload');
        }
      } catch (error: any) {
        lastError = error;
        console.error(`Upload attempt ${attempt} failed:`, error);

        // Don't retry for validation errors or client errors
        if (
          error.message?.includes('validation') ||
          error.message?.includes('Invalid file') ||
          (error.status && error.status >= 400 && error.status < 500)
        ) {
          break;
        }

        // Wait before retry (except for last attempt)
        if (attempt < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, retryDelay * attempt));
        }
      }
    }

    // All attempts failed
    setUploadState({ uploading: false, progress: 0 });
    const errorMessage = getErrorMessage(lastError);
    return {
      success: false,
      error: errorMessage,
    };
  };

  /**
   * Upload multiple files
   */
  const uploadMultipleFiles = async (
    files: File[],
    options: UploadOptions = {}
  ): Promise<{ results: UploadResult[]; successCount: number }> => {
    const results: UploadResult[] = [];
    let successCount = 0;

    setUploadState({ uploading: true, progress: 0 });

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // Update progress for multiple files
      const fileProgress = (progress: number) => {
        const overallProgress = (i * 100 + progress) / files.length;
        setUploadState({ uploading: true, progress: overallProgress });
        options.onProgress?.(overallProgress);
      };

      const result = await uploadFile(file, {
        ...options,
        onProgress: fileProgress,
      });

      results.push(result);
      if (result.success) {
        successCount++;
      }
    }

    setUploadState({ uploading: false, progress: 100 });
    return { results, successCount };
  };

  /**
   * Get user-friendly error message
   */
  const getErrorMessage = (error: any): string => {
    if (!error) return 'Lỗi không xác định';

    if (error.message) {
      if (error.message.includes('timeout')) {
        return 'Quá thời gian chờ. Vui lòng thử lại.';
      }
      if (error.message.includes('network') || error.message.includes('fetch')) {
        return 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.';
      }
      if (error.message.includes('413') || error.message.includes('too large')) {
        return 'File quá lớn. Vui lòng chọn file nhỏ hơn.';
      }
      if (error.message.includes('415') || error.message.includes('unsupported')) {
        return 'Định dạng file không được hỗ trợ.';
      }
      if (error.message.includes('401') || error.message.includes('unauthorized')) {
        return 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';
      }
    }

    return 'Không thể tải lên file. Vui lòng thử lại.';
  };

  /**
   * Reset upload state
   */
  const resetUpload = () => {
    setUploadState({ uploading: false, progress: 0 });
  };

  return {
    uploadFile,
    uploadMultipleFiles,
    uploadState,
    resetUpload,
    validateFile,
  };
};

/**
 * Hook for checking network connectivity
 */
export const useNetworkCheck = () => {
  const { get } = useFetchClient();

  const checkConnectivity = async (): Promise<boolean> => {
    try {
      await get('/admin/init');
      return true;
    } catch {
      return false;
    }
  };

  return { checkConnectivity };
};

// Export upload configuration constants
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  TIMEOUT: 30000,
} as const;
