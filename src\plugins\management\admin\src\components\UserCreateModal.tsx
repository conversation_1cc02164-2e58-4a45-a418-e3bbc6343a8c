import React, { useState, useEffect, useCallback } from 'react';
import { Modal, Form, Input, Select, message, Row, Col, Button as AntButton } from 'antd';
import { useFetchClient } from '@strapi/helper-plugin';
import { Save, Eye, EyeOff } from 'lucide-react';

interface UserCreateModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
}

interface User {
  id: number;
  name: string;
  username: string;
  phone: string;
  email: string;
}

const UserCreateModal: React.FC<UserCreateModalProps> = ({ visible, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [checkingUsername, setCheckingUsername] = useState(false);
  const [checkingEmail, setCheckingEmail] = useState(false);
  const [checkingPhone, setCheckingPhone] = useState(false);
  const { post, get } = useFetchClient();

  // Debounced check functions
  const checkUsername = useCallback(
    async (username: string) => {
      if (!username || username.length < 3) return;

      return new Promise((resolve, reject) => {
        setTimeout(async () => {
          setCheckingUsername(true);
          try {
            const queryParams = new URLSearchParams({
              page: '1',
              pageSize: '1',
              search: username,
            });
            const response = await get(`/management/users?${queryParams}`);
            const users = response.data?.data || [];

            // Check if any user has exact username match
            const existingUser = users.find(
              (user: User) => user.username.toLowerCase() === username.toLowerCase()
            );

            if (existingUser) {
              reject(new Error('Tên đăng nhập đã tồn tại'));
            } else {
              resolve(true);
            }
          } catch (error) {
            console.error('Error checking username:', error);
            reject(new Error('Không thể kiểm tra tên đăng nhập'));
          } finally {
            setCheckingUsername(false);
          }
        }, 500); // 500ms debounce
      });
    },
    [get]
  );

  const checkEmail = useCallback(
    async (email: string) => {
      if (!email || !email.includes('@')) return;

      return new Promise((resolve, reject) => {
        setTimeout(async () => {
          setCheckingEmail(true);
          try {
            const queryParams = new URLSearchParams({
              page: '1',
              pageSize: '1',
              search: email,
            });
            const response = await get(`/management/users?${queryParams}`);
            const users = response.data?.data || [];

            // Check if any user has exact email match
            const existingUser = users.find(
              (user: User) => user.email.toLowerCase() === email.toLowerCase()
            );

            if (existingUser) {
              reject(new Error('Email đã tồn tại'));
            } else {
              resolve(true);
            }
          } catch (error) {
            console.error('Error checking email:', error);
            reject(new Error('Không thể kiểm tra email'));
          } finally {
            setCheckingEmail(false);
          }
        }, 500); // 500ms debounce
      });
    },
    [get]
  );

  const checkPhone = useCallback(
    async (phone: string) => {
      if (!phone || phone.length < 10) return;

      return new Promise((resolve, reject) => {
        setTimeout(async () => {
          setCheckingPhone(true);
          try {
            const queryParams = new URLSearchParams({
              page: '1',
              pageSize: '1',
              search: phone,
            });
            const response = await get(`/management/users?${queryParams}`);
            const users = response.data?.data || [];

            // Check if any user has exact phone match
            const existingUser = users.find((user: User) => user.phone === phone);

            if (existingUser) {
              reject(new Error('Số điện thoại đã tồn tại'));
            } else {
              resolve(true);
            }
          } catch (error) {
            console.error('Error checking phone:', error);
            reject(new Error('Không thể kiểm tra số điện thoại'));
          } finally {
            setCheckingPhone(false);
          }
        }, 500); // 500ms debounce
      });
    },
    [get]
  );

  // Fetch users for referrer selection
  const fetchUsers = async () => {
    setUsersLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: '1',
        pageSize: '1000',
        blocked: 'false',
      });
      const response = await get(`/management/users?${queryParams}`);
      setUsers(response.data?.data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      message.error('Không thể tải danh sách đại lý');
    } finally {
      setUsersLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchUsers();
    }
  }, [visible]);

  const handleSubmit = async (values: any) => {
    if (submitting) return;

    try {
      setSubmitting(true);

      const response = await post('/management/users', values);

      if (response.data?.success) {
        message.success('Tạo đại lý thành công!');
        form.resetFields();
        onSuccess?.();
        onCancel();
      } else {
        message.error('Có lỗi xảy ra khi tạo đại lý');
      }
    } catch (error: any) {
      console.error('Error creating user:', error);

      // Handle specific error messages
      const errorMessage = error.response?.data?.error?.message || error.message;
      if (errorMessage.includes('Username already exists')) {
        message.error('Tên đăng nhập đã tồn tại');
      } else if (errorMessage.includes('Email already exists')) {
        message.error('Email đã tồn tại');
      } else if (errorMessage.includes('Phone number already exists')) {
        message.error('Số điện thoại đã tồn tại');
      } else {
        message.error('Không thể tạo đại lý. Vui lòng thử lại.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={
        <span
          style={{
            fontWeight: 500,
            fontSize: 16,
            color: '#344054',
            fontFamily: "'Be Vietnam Pro', sans-serif",
          }}
        >
          Thêm đại lý mới
        </span>
      }
      open={visible}
      onCancel={handleCancel}
      footer={
        <div style={{ textAlign: 'right' }}>
          <AntButton style={{ marginRight: 8 }} disabled={submitting} onClick={handleCancel}>
            Hủy bỏ
          </AntButton>
          <AntButton
            type="primary"
            icon={<Save size={16} />}
            onClick={() => form.submit()}
            loading={submitting}
          >
            Tạo đại lý
          </AntButton>
        </div>
      }
      width={800}
      style={{ top: 20 }}
      styles={{
        header: {
          borderBottom: '1px solid #e2e8f0',
          paddingBottom: '16px',
          marginBottom: '0',
        },
        body: {
          paddingTop: '24px',
          fontFamily: "'Be Vietnam Pro', sans-serif",
        },
      }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        style={{
          fontFamily: "'Be Vietnam Pro', sans-serif",
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Họ và tên"
              name="name"
              rules={[
                { required: true, message: 'Vui lòng nhập họ và tên' },
                { min: 2, message: 'Họ và tên phải có ít nhất 2 ký tự' },
              ]}
            >
              <Input placeholder="Nhập họ và tên" size="large" style={{ borderRadius: 8 }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Tên đăng nhập"
              name="username"
              rules={[
                { required: true, message: 'Vui lòng nhập tên đăng nhập' },
                { min: 3, message: 'Tên đăng nhập phải có ít nhất 3 ký tự' },
                {
                  pattern: /^[a-zA-Z0-9_]+$/,
                  message: 'Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới',
                },
                { validator: (_, value) => checkUsername(value) },
              ]}
              hasFeedback
              validateStatus={checkingUsername ? 'validating' : undefined}
            >
              <Input placeholder="Nhập tên đăng nhập" size="large" style={{ borderRadius: 8 }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Email"
              name="email"
              rules={[
                { required: true, message: 'Vui lòng nhập email' },
                { type: 'email', message: 'Email không hợp lệ' },
                { validator: (_, value) => checkEmail(value) },
              ]}
              hasFeedback
              validateStatus={checkingEmail ? 'validating' : undefined}
            >
              <Input placeholder="Nhập email" size="large" style={{ borderRadius: 8 }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Số điện thoại"
              name="phone"
              rules={[
                { required: true, message: 'Vui lòng nhập số điện thoại' },
                { pattern: /^[0-9]{10,11}$/, message: 'Số điện thoại phải có 10-11 chữ số' },
                { validator: (_, value) => checkPhone(value) },
              ]}
              hasFeedback
              validateStatus={checkingPhone ? 'validating' : undefined}
            >
              <Input placeholder="Nhập số điện thoại" size="large" style={{ borderRadius: 8 }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="Mật khẩu"
              name="password"
              rules={[
                { required: true, message: 'Vui lòng nhập mật khẩu' },
                { min: 6, message: 'Mật khẩu phải có ít nhất 6 ký tự' },
              ]}
            >
              <Input.Password
                placeholder="Nhập mật khẩu"
                size="large"
                style={{ borderRadius: 8 }}
                iconRender={(visible) => (visible ? <Eye size={16} /> : <EyeOff size={16} />)}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Người giới thiệu (tùy chọn)" name="referUser">
              <Select
                placeholder="Chọn người giới thiệu"
                size="large"
                style={{ borderRadius: 8 }}
                loading={usersLoading}
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
                options={users.map((user) => ({
                  value: user.id,
                  label: `${user.name} - ${user.phone}`,
                }))}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default UserCreateModal;
