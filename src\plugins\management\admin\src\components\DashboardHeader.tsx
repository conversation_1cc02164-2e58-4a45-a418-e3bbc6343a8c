import React, { useState, useEffect, useRef } from 'react';
import { Search, Bell, User, Package, Clock } from 'lucide-react';
import styled from 'styled-components';
import { Badge, Spin, Empty } from 'antd';
import { useFetchClient } from '@strapi/helper-plugin';
import { useNavigate } from 'react-router-dom';

const HeaderContainer = styled.div`
  background: #ffffff;
  padding: 16px 24px;
  position: fixed;
  top: 0;
  right: 0;
  left: 256px;
  z-index: 999;
  transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &.collapsed {
    left: 70px;
  }
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
`;

const BreadcrumbText = styled.span`
  color: #6b7280;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', sans-serif;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const SearchContainer = styled.div`
  position: relative;
`;

const SearchIcon = styled(Search)`
  width: 16px;
  height: 16px;
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
`;

const SearchInput = styled.input`
  padding: 8px 16px 8px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Be Vietnam Pro', sans-serif;
  outline: none;
  transition: all 0.2s ease;
  width: 240px;

  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const NotificationButton = styled.button`
  position: relative;
  padding: 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const BellIcon = styled(Bell)`
  width: 20px;
  height: 20px;
  color: #6b7280;
`;

const NotificationContainer = styled.div`
  position: relative;
`;

const NotificationDropdown = styled.div<{ visible: boolean }>`
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 1000;
  margin-top: 8px;
  display: ${(props) => (props.visible ? 'block' : 'none')};
`;

const NotificationHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
`;

const NotificationTitle = styled.h4`
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  font-family: 'Be Vietnam Pro', sans-serif;
`;

const NotificationList = styled.div`
  max-height: 300px;
  overflow-y: auto;
`;

const NotificationItem = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
`;

const NotificationContent = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const NotificationIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #e6f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
`;

const NotificationText = styled.div`
  flex: 1;
`;

const NotificationMessage = styled.div`
  font-size: 13px;
  color: #262626;
  font-family: 'Be Vietnam Pro', sans-serif;
  margin-bottom: 4px;
`;

const NotificationTime = styled.div`
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Be Vietnam Pro', sans-serif;
`;

const NotificationEmpty = styled.div`
  padding: 40px 16px;
  text-align: center;
  color: #8c8c8c;
  font-family: 'Be Vietnam Pro', sans-serif;
`;

interface NotificationData {
  id: string;
  type: 'order' | 'user' | 'withdrawal';
  title: string;
  message: string;
  time: string;
  data?: any;
}

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  background-color: #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const UserIcon = styled(User)`
  width: 16px;
  height: 16px;
  color: #6b7280;
`;

const UserName = styled.span`
  font-size: 14px;
  color: #374151;
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 500;
`;

interface DashboardHeaderProps {
  collapsed?: boolean;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ collapsed = false }) => {
  const { get } = useFetchClient();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      // Fetch pending orders
      const { data: ordersData } = await get('/management/orders', {
        params: { status: 'Chờ xác nhận', pageSize: 10 },
      });

      const pendingOrders = ordersData?.data || [];

      // Create notifications from pending orders
      const orderNotifications: NotificationData[] = pendingOrders.map((order: any) => ({
        id: `order-${order.id}`,
        type: 'order' as const,
        title: 'Đơn hàng mới',
        message: `Đơn hàng ${order.code} cần được xác nhận`,
        time: formatTimeAgo(order.createdAt),
        data: order,
      }));

      setNotifications(orderNotifications);
      setNotificationCount(orderNotifications.length);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} giờ trước`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} ngày trước`;
  };

  // Handle notification click
  const handleNotificationClick = (notification: NotificationData) => {
    if (notification.type === 'order') {
      navigate(`orders`);
    }
    setDropdownVisible(false);
  };

  // Fetch notifications on component mount and set up polling
  useEffect(() => {
    fetchNotifications();

    // Poll for new notifications every 30 seconds
    const interval = setInterval(fetchNotifications, 30000);

    return () => clearInterval(interval);
  }, []);

  // Handle click outside to close dropdown
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownVisible(false);
      }
    };

    if (dropdownVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownVisible]);

  return (
    <HeaderContainer className={collapsed ? 'collapsed' : ''}>
      <HeaderContent>
        <LeftSection></LeftSection>

        <RightSection>
          <SearchContainer>
            <SearchIcon />
            <SearchInput type="text" placeholder="Search..." />
          </SearchContainer>

          <NotificationContainer ref={dropdownRef}>
            <NotificationButton onClick={() => setDropdownVisible(!dropdownVisible)}>
              <Badge count={notificationCount} size="small" offset={[-2, 2]}>
                <BellIcon />
              </Badge>
            </NotificationButton>

            <NotificationDropdown visible={dropdownVisible}>
              <NotificationHeader>
                <NotificationTitle>Thông báo ({notificationCount})</NotificationTitle>
              </NotificationHeader>

              {loading ? (
                <div style={{ padding: '20px', textAlign: 'center' }}>
                  <Spin size="small" />
                </div>
              ) : notifications.length > 0 ? (
                <NotificationList>
                  {notifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <NotificationContent>
                        <NotificationIcon>
                          {notification.type === 'order' && <Package size={16} color="#1890ff" />}
                          {notification.type === 'user' && <User size={16} color="#52c41a" />}
                          {notification.type === 'withdrawal' && (
                            <Clock size={16} color="#faad14" />
                          )}
                        </NotificationIcon>
                        <NotificationText>
                          <NotificationMessage>{notification.message}</NotificationMessage>
                          <NotificationTime>{notification.time}</NotificationTime>
                        </NotificationText>
                      </NotificationContent>
                    </NotificationItem>
                  ))}
                </NotificationList>
              ) : (
                <NotificationEmpty>
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="Không có thông báo mới"
                  />
                </NotificationEmpty>
              )}
            </NotificationDropdown>
          </NotificationContainer>

          <UserSection>
            <UserAvatar>
              <UserIcon />
            </UserAvatar>
            <UserName>Admin</UserName>
          </UserSection>
        </RightSection>
      </HeaderContent>
    </HeaderContainer>
  );
};

export default DashboardHeader;
