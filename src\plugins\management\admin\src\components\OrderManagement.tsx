import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import 'dayjs/locale/vi';
import * as XLSX from 'xlsx';
import {
  ShoppingCart,
  Eye,
  Filter,
  Download,
  RefreshCw,
  Clock,
  Truck,
  CheckCircle2,
  AlertCircle,
  Send,
  ArrowUpDown,
  FileText,
  Gift,
  X,
  Percent,
  Plus,
  Printer,
} from 'lucide-react';
import {
  Modal,
  message,
  Spin,
  Table as AntTable,
  Empty,
  Button as AntButton,
  Row,
  Col,
  Tag,
  Space,
  Tooltip,
  Radio,
  DatePicker,
  Checkbox,
  Badge,
  Tabs,
  Card as AntCard,
  Drawer,
  Select,
  Input,
  AutoComplete,
} from 'antd';
import { useFetchClient } from '@strapi/helper-plugin';
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  StatsGrid,
  StatsCard,
  FilterGroup,
  FilterLabel,
  DateInput,
  ActionButtonGroup,
  StyledTable,
} from './shared';
import './OrderManagement.css';

// Set dayjs locale to Vietnamese
dayjs.locale('vi');

const { RangePicker } = DatePicker;

// Order status options - Unified color scheme
const statusOptions = [
  { value: '', label: 'Tất cả', color: '#64748b', icon: <Filter size={16} /> },
  { value: 'Chờ xác nhận', label: 'Chờ xác nhận', color: '#f59e0b', icon: <Clock size={16} /> },
  { value: 'Chờ giao hàng', label: 'Chờ giao hàng', color: '#3b82f6', icon: <Send size={16} /> },
  { value: 'Đang giao hàng', label: 'Đang giao hàng', color: '#8b5cf6', icon: <Truck size={16} /> },
  {
    value: 'Đã hoàn thành',
    label: 'Đã hoàn thành',
    color: '#10b981',
    icon: <CheckCircle2 size={16} />,
  },
  { value: 'Đã hủy', label: 'Đã hủy', color: '#ef4444', icon: <AlertCircle size={16} /> },
];

// Define valid status transitions
const statusTransitions: Record<string, string[]> = {
  'Chờ xác nhận': ['Chờ giao hàng', 'Đã hủy'],
  'Chờ giao hàng': ['Đang giao hàng', 'Đã hủy'],
  'Đang giao hàng': ['Đã hoàn thành', 'Đã hủy'],
  'Đã hoàn thành': [],
  'Đã hủy': [],
};

interface Order {
  id: string;
  code: string;
  statusOrder: string;
  priceAfterTax: number;
  priceBeforeTax: number;
  taxAmount: number;
  customer?: {
    name: string;
    phone: string;
    email: string;
    address: string;
    ward: string;
    district: string;
    city: string;
  };
  products?: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  createdAt: string;
  updatedAt: string;
}

interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  shippingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
}

interface User {
  id: number;
  username: string;
  name: string;
  phone: string;
  email: string;
  blocked: boolean;
}

const OrderManagement = () => {
  const { get, put, post } = useFetchClient();

  // State management
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [exporting, setExporting] = useState(false);

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    status: 'Chờ xác nhận',
    dateFrom: '',
    dateTo: '',
  });

  // Pagination
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0,
    pageCount: 0,
  });

  // Stats
  const [stats, setStats] = useState<OrderStats>({
    totalOrders: 0,
    pendingOrders: 0,
    shippingOrders: 0,
    completedOrders: 0,
    cancelledOrders: 0,
    totalRevenue: 0,
  });

  // Export modal states
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportDateRange, setExportDateRange] = useState<[any, any] | null>(null);
  const [exportStatuses, setExportStatuses] = useState<string[]>([]);

  // Order detail modal states
  const [orderDetailVisible, setOrderDetailVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [orderDetailLoading, setOrderDetailLoading] = useState(false);

  // Create order modal states
  const [createOrderVisible, setCreateOrderVisible] = useState(false);
  const [createOrderLoading, setCreateOrderLoading] = useState(false);

  // User selection states
  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [customerFormData, setCustomerFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    ward: '',
    district: '',
    city: '',
  });

  // Product selection states
  const [products, setProducts] = useState<any[]>([]);
  const [productsLoading, setProductsLoading] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [productSearchTerm, setProductSearchTerm] = useState('');

  // Order calculation states
  const [taxPercentage, setTaxPercentage] = useState<number>(0);

  // Helper functions
  const getStatusConfig = (status: string) => {
    return statusOptions.find((opt) => opt.value === status) || statusOptions[0];
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount);
  };

  const getAvailableStatusOptions = (currentStatus: string) => {
    const availableStatuses = statusTransitions[currentStatus] || [];
    return statusOptions.filter((option) => availableStatuses.includes(option.value));
  };

  // Order steps helper function
  const getOrderSteps = (currentStatus: string) => {
    const steps = [
      {
        title: 'Chờ duyệt',
        description: 'Đơn hàng đang chờ xác nhận',
        icon: <FileText size={16} />,
        status: 'Chờ xác nhận',
        color: '#f59e0b',
      },
      {
        title: 'Chờ giao hàng',
        description: 'Đơn hàng đã được xác nhận, chuẩn bị giao',
        icon: <Clock size={16} />,
        status: 'Chờ giao hàng',
        color: '#3b82f6',
      },
      {
        title: 'Đang giao hàng',
        description: 'Đơn hàng đang được vận chuyển',
        icon: <Truck size={16} />,
        status: 'Đang giao hàng',
        color: '#8b5cf6',
      },
      {
        title: 'Hoàn thành',
        description: 'Đơn hàng đã được giao thành công',
        icon: <Gift size={16} />,
        status: 'Đã hoàn thành',
        color: '#10b981',
      },
    ];

    // Handle cancelled orders
    if (currentStatus === 'Đã hủy') {
      return [
        {
          title: 'Đã hủy',
          description: 'Đơn hàng đã bị hủy',
          icon: <X size={16} />,
          stepStatus: 'error',
          status: 'Đã hủy',
          color: '#ef4444',
          isActive: true,
        },
      ];
    }

    // Find current step index based on current status
    let currentStepIndex = 0;
    switch (currentStatus) {
      case 'Chờ xác nhận':
        currentStepIndex = 0;
        break;
      case 'Chờ giao hàng':
        currentStepIndex = 1;
        break;
      case 'Đang giao hàng':
        currentStepIndex = 2;
        break;
      case 'Đã hoàn thành':
        currentStepIndex = 3;
        break;
      default:
        currentStepIndex = 0;
    }

    return steps.map((step, index) => ({
      ...step,
      stepStatus:
        index < currentStepIndex ? 'finish' : index === currentStepIndex ? 'process' : 'wait',
      isActive: index === currentStepIndex,
    }));
  };

  // Data fetching functions
  const fetchOrders = async () => {
    setLoading(true);
    try {
      const params: any = {
        page: pagination.page,
        pageSize: pagination.pageSize,
      };

      if (filters.status) params.status = filters.status;
      if (filters.search) params.code = filters.search;
      if (filters.dateFrom) params.startDate = filters.dateFrom;
      if (filters.dateTo) params.endDate = filters.dateTo;

      const { data } = await get('/management/orders', { params });
      setOrders(data.data || []);
      setPagination((prev) => ({
        ...prev,
        total: data.total || 0,
        pageCount: data.pageCount || 0,
      }));
    } catch (error) {
      console.error('Error fetching orders:', error);
      message.error('Không thể tải danh sách đơn hàng');
    } finally {
      setLoading(false);
    }
  };

  const fetchAllOrders = async () => {
    try {
      const { data } = await get('/management/orders', {
        params: { page: 1, pageSize: 1000 },
      });
      const allOrdersData = data.data || [];

      // Calculate stats
      const newStats = {
        totalOrders: allOrdersData.length,
        pendingOrders: allOrdersData.filter((o: Order) => o.statusOrder === 'Chờ xác nhận').length,
        shippingOrders: allOrdersData.filter((o: Order) => o.statusOrder === 'Đang giao hàng')
          .length,
        completedOrders: allOrdersData.filter((o: Order) => o.statusOrder === 'Đã hoàn thành')
          .length,
        cancelledOrders: allOrdersData.filter((o: Order) => o.statusOrder === 'Đã hủy').length,
        totalRevenue: allOrdersData
          .filter((o: Order) => o.statusOrder === 'Đã hoàn thành')
          .reduce((sum: number, o: Order) => sum + (o.priceAfterTax || 0), 0),
      };
      setStats(newStats);
    } catch (error) {
      console.error('Error fetching all orders:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([fetchOrders(), fetchAllOrders()]);
      message.success('Đã làm mới danh sách đơn hàng');
    } catch (error) {
      message.error('Không thể làm mới dữ liệu');
    } finally {
      setRefreshing(false);
    }
  };

  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      await put(`/management/orders/${id}/status`, { status: newStatus });
      message.success('Cập nhật trạng thái thành công');
      await Promise.all([fetchOrders(), fetchAllOrders()]);
    } catch (error) {
      console.error('Error updating order status:', error);
      message.error('Không thể cập nhật trạng thái đơn hàng');
    }
  };

  // Fetch order detail
  const fetchOrderDetail = async (orderId: string) => {
    setOrderDetailLoading(true);
    try {
      const { data } = await get(`/management/orders/${orderId}`);
      setSelectedOrder(data);
    } catch (error) {
      console.error('Error fetching order detail:', error);
      message.error('Không thể tải chi tiết đơn hàng');
    } finally {
      setOrderDetailLoading(false);
    }
  };

  // Handle view order detail
  const handleViewOrder = async (orderId: string) => {
    setOrderDetailVisible(true);
    await fetchOrderDetail(orderId);
  };

  // Handle print invoice
  const handlePrintInvoice = () => {
    if (!selectedOrder) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      message.error('Không thể mở cửa sổ in. Vui lòng kiểm tra trình duyệt.');
      return;
    }

    const invoiceHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Hóa đơn - ${selectedOrder.code}</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
          }
          .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
          }
          .invoice-header {
            text-align: center;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
          }
          .invoice-title {
            font-size: 20px;
            font-weight: bold;
            margin: 20px 0;
            color: #1e293b;
          }
          .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
          }
          .info-section {
            flex: 1;
          }
          .info-section h3 {
            color: #2563eb;
            margin-bottom: 10px;
            font-size: 16px;
          }
          .info-row {
            margin-bottom: 8px;
          }
          .label {
            font-weight: bold;
            color: #64748b;
          }
          .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
          }
          .products-table th,
          .products-table td {
            border: 1px solid #e2e8f0;
            padding: 12px;
            text-align: left;
          }
          .products-table th {
            background-color: #f8fafc;
            font-weight: bold;
            color: #374151;
          }
          .products-table .text-right {
            text-align: right;
          }
          .products-table .text-center {
            text-align: center;
          }
          .total-section {
            float: right;
            width: 300px;
            margin-top: 20px;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
          }
          .total-row.final {
            border-top: 2px solid #2563eb;
            border-bottom: 2px solid #2563eb;
            font-weight: bold;
            font-size: 16px;
            color: #2563eb;
          }
          .footer {
            margin-top: 50px;
            text-align: center;
            color: #64748b;
            font-size: 14px;
          }
          @media print {
            body { margin: 0; }
            .invoice-container { box-shadow: none; }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="invoice-header">
            <div class="company-name">CÔNG TY CỔ PHẦN ABC</div>
            <div>Địa chỉ: 123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh</div>
            <div>Điện thoại: (028) 1234 5678 | Email: <EMAIL></div>
            <div class="invoice-title">HÓA ĐƠN BÁN HÀNG</div>
            <div><strong>Số: ${selectedOrder.code}</strong></div>
          </div>

          <div class="invoice-info">
            <div class="info-section">
              <h3>Thông tin khách hàng</h3>
              <div class="info-row">
                <span class="label">Tên khách hàng:</span> ${selectedOrder.customer?.name || 'N/A'}
              </div>
              <div class="info-row">
                <span class="label">Điện thoại:</span> ${selectedOrder.customer?.phone || 'N/A'}
              </div>
              <div class="info-row">
                <span class="label">Email:</span> ${selectedOrder.customer?.email || 'N/A'}
              </div>
              <div class="info-row">
                <span class="label">Địa chỉ:</span> ${
                  [
                    selectedOrder.customer?.address,
                    selectedOrder.customer?.ward,
                    selectedOrder.customer?.district,
                    selectedOrder.customer?.city,
                  ]
                    .filter(Boolean)
                    .join(', ') || 'N/A'
                }
              </div>
            </div>
            <div class="info-section">
              <h3>Thông tin đơn hàng</h3>
              <div class="info-row">
                <span class="label">Mã đơn hàng:</span> ${selectedOrder.code}
              </div>
              <div class="info-row">
                <span class="label">Ngày tạo:</span> ${dayjs(selectedOrder.createdAt).format('DD/MM/YYYY HH:mm')}
              </div>
              <div class="info-row">
                <span class="label">Trạng thái:</span> ${selectedOrder.statusOrder}
              </div>
            </div>
          </div>

          <table class="products-table">
            <thead>
              <tr>
                <th>STT</th>
                <th>Tên sản phẩm</th>
                <th class="text-center">Số lượng</th>
                <th class="text-right">Đơn giá</th>
                <th class="text-right">Thành tiền</th>
              </tr>
            </thead>
            <tbody>
              ${
                selectedOrder.products
                  ?.map(
                    (product: any, index: number) => `
                <tr>
                  <td class="text-center">${index + 1}</td>
                  <td>${product.name}</td>
                  <td class="text-center">${product.quantity}</td>
                  <td class="text-right">${formatCurrency(product.price)}</td>
                  <td class="text-right">${formatCurrency(product.price * product.quantity)}</td>
                </tr>
              `
                  )
                  .join('') || '<tr><td colspan="5" class="text-center">Không có sản phẩm</td></tr>'
              }
            </tbody>
          </table>

          <div class="total-section">
            <div class="total-row">
              <span>Tiền hàng:</span>
              <span>${formatCurrency(selectedOrder.priceAfterTax - selectedOrder.taxAmount)}</span>
            </div>
            <div class="total-row">
              <span>Thuế:</span>
              <span>${formatCurrency(selectedOrder.taxAmount || 0)}</span>
            </div>
            <div class="total-row final">
              <span>Tổng cộng:</span>
              <span>${formatCurrency(selectedOrder.priceAfterTax)}</span>
            </div>
          </div>

          <div style="clear: both;"></div>

          <div class="footer">
            <p>Cảm ơn quý khách đã sử dụng dịch vụ của chúng tôi!</p>
            <p>Hóa đơn được in vào ngày ${dayjs().format('DD/MM/YYYY HH:mm')}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(invoiceHTML);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };

  // Fetch users for customer selection
  const fetchUsers = async () => {
    setUsersLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: '1',
        pageSize: '1000',
        blocked: 'false',
      });

      const response = await get(`/management/users?${queryParams}`);
      console.log('Users response:', response.data);
      setUsers(response.data.data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      message.error('Không thể tải danh sách đại lý');
    } finally {
      setUsersLoading(false);
    }
  };

  // Handle user selection
  const handleUserSelect = (userId: number) => {
    const selectedUser = users.find((user) => user.id === userId);
    if (selectedUser) {
      setSelectedUserId(userId);
      setCustomerFormData({
        name: selectedUser.name || '',
        phone: selectedUser.phone || '',
        email: selectedUser.email || '',
        address: '',
        ward: '',
        district: '',
        city: '',
      });
    }
  };

  // Reset customer form
  const resetCustomerForm = () => {
    setSelectedUserId(null);
    setCustomerFormData({
      name: '',
      phone: '',
      email: '',
      address: '',
      ward: '',
      district: '',
      city: '',
    });
  };

  // Helper function to sanitize search term
  const sanitizeSearchTerm = (term: string) => {
    if (!term) return '';

    // Remove or escape special characters that might cause issues
    return term
      .trim()
      .replace(/[<>]/g, '') // Remove < and > characters
      .replace(/['"]/g, '') // Remove quotes
      .replace(/[\\]/g, '') // Remove backslashes
      .replace(/[{}]/g, '') // Remove curly braces
      .replace(/[\[\]]/g, '') // Remove square brackets
      .replace(/[|]/g, '') // Remove pipe characters
      .replace(/[&]/g, '') // Remove ampersand
      .substring(0, 100); // Limit length to prevent overly long queries
  };

  // Fetch products for selection
  const fetchProducts = async (searchTerm = '') => {
    setProductsLoading(true);
    try {
      // Sanitize search term
      const sanitizedTerm = sanitizeSearchTerm(searchTerm);

      // Skip search if term is too short after sanitization
      if (searchTerm && sanitizedTerm.length < 1) {
        setProducts([]);
        setProductsLoading(false);
        return;
      }

      const queryParams = new URLSearchParams({
        page: '1',
        pageSize: '100',
        status: 'true', // Only active products
        ...(sanitizedTerm && { search: sanitizedTerm }),
      });

      const response = await get(`/management/products?${queryParams}`);
      setProducts(response.data.data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
      message.error('Không thể tải danh sách sản phẩm');
      setProducts([]); // Clear products on error
    } finally {
      setProductsLoading(false);
    }
  };

  // Handle product selection
  const handleProductSelect = (productId: string, quantity: number = 1) => {
    const product = products.find((p) => p.id === productId);
    if (product) {
      const imageUrl =
        product.hinh_anh && product.hinh_anh.length > 0
          ? product.hinh_anh[0]?.formats?.thumbnail?.url ||
            product.hinh_anh[0]?.formats?.small?.url ||
            product.hinh_anh[0]?.url
          : null;

      const existingIndex = selectedProducts.findIndex((p) => p.id === productId);
      if (existingIndex >= 0) {
        // Update existing product quantity
        const updatedProducts = [...selectedProducts];
        updatedProducts[existingIndex] = {
          ...updatedProducts[existingIndex],
          quantity: quantity,
          total: product.gia_ban * quantity,
        };
        setSelectedProducts(updatedProducts);
      } else {
        // Add new product
        setSelectedProducts([
          ...selectedProducts,
          {
            id: product.id,
            name: product.name,
            price: product.gia_ban || 0,
            quantity: quantity,
            total: (product.gia_ban || 0) * quantity,
            imageUrl: imageUrl,
          },
        ]);
      }
    }
  };

  // Remove product from selection
  const handleRemoveProduct = (productId: string) => {
    setSelectedProducts(selectedProducts.filter((p) => p.id !== productId));
  };

  // Calculate order totals
  const calculateOrderTotals = () => {
    const subtotal = selectedProducts.reduce((sum, product) => sum + product.total, 0);
    const tax = (subtotal * taxPercentage) / 100;
    const total = subtotal + tax;
    return { subtotal, tax, total };
  };

  // Handle create order
  const handleCreateOrder = async () => {
    try {
      // Validate form data
      if (!customerFormData.name.trim()) {
        message.error('Vui lòng nhập tên khách hàng');
        return;
      }

      if (!customerFormData.phone.trim()) {
        message.error('Vui lòng nhập số điện thoại khách hàng');
        return;
      }

      if (selectedProducts.length === 0) {
        message.error('Vui lòng chọn ít nhất một sản phẩm');
        return;
      }

      setCreateOrderLoading(true);

      // Prepare order data
      const orderData = {
        customer: customerFormData,
        products: selectedProducts.map((product) => ({
          id: product.id,
          name: product.name,
          price: product.price,
          quantity: product.quantity,
        })),
        taxPercentage: taxPercentage,
        userId: selectedUserId,
      };

      // Create order
      await post('/management/orders', { orderData });

      message.success('Tạo đơn hàng thành công');

      // Close drawer and reset form
      setCreateOrderVisible(false);
      resetCustomerForm();
      setSelectedProducts([]);
      setProductSearchTerm('');
      setTaxPercentage(0);

      // Refresh orders list
      await Promise.all([fetchOrders(), fetchAllOrders()]);
    } catch (error) {
      console.error('Error creating order:', error);
      message.error('Không thể tạo đơn hàng');
    } finally {
      setCreateOrderLoading(false);
    }
  };

  // Excel export function
  const createAndDownloadExcel = async (orders: Order[]) => {
    const workbook = XLSX.utils.book_new();

    const excelData = orders.map((order, index) => {
      const customer = order.customer || {};
      const products = order.products || [];

      let productNames = 'Không có sản phẩm';
      let productQuantities = 'N/A';
      let productPrices = 'N/A';

      if (products.length > 0) {
        productNames = products.map((p: any) => p?.name || 'N/A').join(', ');
        productQuantities = products.map((p: any) => p?.quantity || 0).join(', ');
        productPrices = products
          .map((p: any) => (p?.price || 0).toLocaleString('vi-VN'))
          .join(', ');
      }

      return {
        STT: index + 1,
        ID: order.id,
        'Mã đơn hàng': order.code,
        'Trạng thái': order.statusOrder,
        'Tên khách hàng': (customer as any)?.name || 'N/A',
        'Số điện thoại': (customer as any)?.phone || 'N/A',
        Email: (customer as any)?.email || 'N/A',
        'Địa chỉ': (customer as any)?.address
          ? `${(customer as any)?.address}, ${(customer as any)?.ward || ''}, ${(customer as any)?.district || ''}, ${(customer as any)?.city || ''}`
              .replace(/,\s*,/g, ',')
              .replace(/^,|,$/g, '')
          : 'N/A',
        'Danh sách sản phẩm': productNames,
        'Số lượng': productQuantities,
        'Đơn giá': productPrices,
        'Tổng tiền': order.priceAfterTax || 0,
        'Ngày tạo': order.createdAt ? dayjs(order.createdAt).format('DD/MM/YYYY HH:mm:ss') : 'N/A',
      };
    });

    const worksheet = XLSX.utils.json_to_sheet(excelData);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Danh sách đơn hàng');

    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
    const filename = `don-hang-${dateStr}-${timeStr}.xlsx`;

    XLSX.writeFile(workbook, filename);
  };

  const handleExportConfirm = async () => {
    setExporting(true);
    try {
      const queryParams: any = {
        page: 1,
        pageSize: 10000,
      };

      if (exportDateRange && exportDateRange[0] && exportDateRange[1]) {
        queryParams.startDate = exportDateRange[0].format('YYYY-MM-DD');
        queryParams.endDate = exportDateRange[1].format('YYYY-MM-DD');
      }

      if (exportStatuses.length === 1) {
        queryParams.status = exportStatuses[0];
      }

      const { data: ordersData } = await get('/management/orders', { params: queryParams });
      let ordersToExport = ordersData.data || [];

      if (exportStatuses.length > 1) {
        ordersToExport = ordersToExport.filter((order: Order) =>
          exportStatuses.includes(order.statusOrder)
        );
      }

      if (ordersToExport.length === 0) {
        message.warning('Không có đơn hàng nào phù hợp với bộ lọc đã chọn');
        setExportModalVisible(false);
        return;
      }

      await createAndDownloadExcel(ordersToExport);
      setExportModalVisible(false);
      message.success(`Đã tải xuống file Excel với ${ordersToExport.length} đơn hàng!`);
    } catch (error) {
      console.error('Error exporting orders:', error);
      message.error('Không thể xuất file Excel');
    } finally {
      setExporting(false);
    }
  };

  // Stats data for display - Unified color scheme
  const statsData = [
    {
      title: 'Tổng đơn hàng',
      value: stats.totalOrders,
      icon: ShoppingCart,
      color: '#64748b',
    },
    {
      title: 'Chờ xác nhận',
      value: stats.pendingOrders,
      icon: Clock,
      color: '#f59e0b',
    },
    {
      title: 'Đang giao hàng',
      value: stats.shippingOrders,
      icon: Truck,
      color: '#8b5cf6',
    },
    {
      title: 'Hoàn thành',
      value: stats.completedOrders,
      icon: CheckCircle2,
      color: '#10b981',
    },
  ];

  // Status Update Component
  const StatusUpdateButton: React.FC<{
    currentStatus: string;
    orderId: string;
    orderCode: string;
  }> = ({ currentStatus, orderId, orderCode }) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState<string>('');
    const [updating, setUpdating] = useState(false);

    const availableOptions = getAvailableStatusOptions(currentStatus);
    const canUpdate = availableOptions.length > 0;

    const handleUpdate = async () => {
      if (!selectedStatus) return;

      setUpdating(true);
      try {
        await handleStatusChange(orderId, selectedStatus);
        setIsModalVisible(false);
        setSelectedStatus('');
      } catch (error) {
        console.error('Error updating status:', error);
      } finally {
        setUpdating(false);
      }
    };

    if (!canUpdate) {
      return (
        <Tooltip title="Không thể thay đổi trạng thái">
          <AntButton type="text" icon={<ArrowUpDown size={14} />} size="small" disabled>
            Cập nhật
          </AntButton>
        </Tooltip>
      );
    }

    return (
      <>
        <Tooltip title="Cập nhật trạng thái đơn hàng">
          <AntButton
            type="text"
            icon={<ArrowUpDown size={14} />}
            size="small"
            style={{ color: '#1890ff' }}
            onClick={() => setIsModalVisible(true)}
          >
            Cập nhật
          </AntButton>
        </Tooltip>

        <Modal
          title="Cập nhật trạng thái đơn hàng"
          open={isModalVisible}
          onCancel={() => {
            setIsModalVisible(false);
            setSelectedStatus('');
          }}
          onOk={handleUpdate}
          confirmLoading={updating}
          okText="Cập nhật"
          cancelText="Hủy"
        >
          <div style={{ marginBottom: 16 }}>
            <div style={{ fontWeight: 500, marginBottom: 8 }}>
              Đơn hàng: <span style={{ color: '#1890ff' }}>{orderCode}</span>
            </div>
            <div style={{ marginBottom: 16 }}>
              Trạng thái hiện tại:
              <Tag color={getStatusConfig(currentStatus).color} style={{ marginLeft: 8 }}>
                {currentStatus}
              </Tag>
            </div>
          </div>

          <div style={{ marginBottom: 16 }}>
            <div style={{ fontWeight: 500, marginBottom: 12 }}>Chọn trạng thái mới:</div>
            <Radio.Group
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              style={{ width: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                {availableOptions.map((option) => (
                  <Radio key={option.value} value={option.value}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      {option.icon}
                      <span>{option.label}</span>
                    </div>
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
          </div>
        </Modal>
      </>
    );
  };

  // Table columns definition
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (value: string) => (
        <Badge count={value} style={{ backgroundColor: '#f0f0f0', color: '#666' }} />
      ),
    },
    {
      title: 'Mã đơn hàng',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (value: string) => <div style={{ fontWeight: 500, color: '#2563eb' }}>{value}</div>,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'statusOrder',
      key: 'statusOrder',
      width: 160,
      render: (value: string) => {
        const config = getStatusConfig(value);
        return (
          <Tag
            color={config.color}
            style={{
              margin: 0,
              padding: '6px 12px',
              borderRadius: 8,
              fontWeight: 500,
              fontSize: '13px',
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              width: 'fit-content',
            }}
          >
            {config.icon}
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'priceAfterTax',
      key: 'priceAfterTax',
      width: 150,
      render: (value: number) => (
        <div style={{ fontWeight: 500, color: '#374151', fontSize: '14px' }}>
          {formatCurrency(value)}
        </div>
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (value: string) => {
        if (!value) return '-';
        const date = new Date(value);
        return (
          <div>
            <div style={{ fontWeight: 500 }}>{date.toLocaleDateString('vi-VN')}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {date.toLocaleTimeString('vi-VN')}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 200,
      render: (_: any, record: Order) => (
        <ActionButtonGroup
          onView={() => handleViewOrder(record.id)}
          onEdit={() => {}}
          onDelete={() => {}}
          showView={true}
          showEdit={false}
          showDelete={false}
          viewTooltip="Xem chi tiết đơn hàng"
          customActions={
            <StatusUpdateButton
              currentStatus={record.statusOrder}
              orderId={record.id}
              orderCode={record.code}
            />
          }
        />
      ),
    },
  ];

  // Effects
  useEffect(() => {
    fetchOrders();
  }, [pagination.page, pagination.pageSize, filters]);

  useEffect(() => {
    fetchAllOrders();
  }, []);

  // Fetch users and products when create order drawer opens
  useEffect(() => {
    if (createOrderVisible) {
      fetchUsers();
      fetchProducts();
    }
  }, [createOrderVisible]);

  return (
    <PageContainer className="order-management">
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* Order Management */}
        <Card>
          <PageHeader
            title="Danh sách đơn hàng"
            description="Xem và quản lý danh sách đơn hàng"
            actions={
              <Space>
                <AntButton
                  icon={<RefreshCw size={16} />}
                  onClick={handleRefresh}
                  loading={refreshing}
                  style={{
                    borderRadius: '6px',
                    fontWeight: 500,
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                >
                  Làm mới
                </AntButton>
                <AntButton
                  icon={<Download size={16} />}
                  onClick={() => setExportModalVisible(true)}
                  loading={exporting}
                  style={{
                    borderRadius: '6px',
                    fontWeight: 500,
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                    background: '#059669',
                    borderColor: '#059669',
                    color: '#ffffff',
                  }}
                >
                  Xuất Excel
                </AntButton>
                <AntButton
                  icon={<Plus size={16} />}
                  onClick={() => setCreateOrderVisible(true)}
                  type="primary"
                  style={{
                    borderRadius: '6px',
                    fontWeight: 500,
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                    background: '#2563eb',
                    borderColor: '#2563eb',
                  }}
                >
                  Tạo đơn hàng
                </AntButton>
              </Space>
            }
          />

          <CardContent>
            {/* Status Tabs */}
            <div style={{ marginBottom: 10 }}>
              <Tabs
                activeKey={filters.status}
                onChange={(key) => setFilters({ ...filters, status: key })}
                type="card"
                size="large"
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
                items={statusOptions.map((option) => ({
                  key: option.value,
                  label: (
                    <div
                      style={{ display: 'flex', alignItems: 'center', gap: 8, padding: '4px 8px' }}
                    >
                      {option.icon}
                      <span>{option.label}</span>
                      {option.value === 'Chờ xác nhận' && stats.pendingOrders > 0 && (
                        <Badge
                          count={stats.pendingOrders}
                          style={{
                            backgroundColor: 'red',
                            fontSize: '11px',
                            height: '18px',
                            minWidth: '18px',
                            lineHeight: '18px',
                            padding: '0 6px',
                            borderRadius: '9px',
                          }}
                        />
                      )}
                      {option.value === 'Chờ giao hàng' && stats.shippingOrders > 0 && (
                        <Badge
                          count={stats.shippingOrders}
                          style={{
                            backgroundColor: 'red',
                            fontSize: '11px',
                            height: '18px',
                            minWidth: '18px',
                            lineHeight: '18px',
                            padding: '0 6px',
                            borderRadius: '9px',
                          }}
                        />
                      )}
                    </div>
                  ),
                }))}
              />
            </div>

            {/* Filters Section */}
            <FiltersSection>
              <FilterGroup>
                <FilterLabel>Tìm kiếm</FilterLabel>
                <SearchBar
                  placeholder="Nhập mã đơn hàng..."
                  value={filters.search}
                  onChange={(value) => setFilters({ ...filters, search: value })}
                />
              </FilterGroup>

              <FilterGroup>
                <FilterLabel>Từ ngày</FilterLabel>
                <DateInput
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
                />
              </FilterGroup>

              <FilterGroup>
                <FilterLabel>Đến ngày</FilterLabel>
                <DateInput
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
                />
              </FilterGroup>
            </FiltersSection>

            {/* Orders Table */}
            <StyledTable>
              <AntTable
                columns={columns}
                dataSource={orders}
                rowKey="id"
                loading={loading}
                pagination={{
                  current: pagination.page,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  onChange: (page, pageSize) => {
                    setPagination((prev) => ({ ...prev, page, pageSize: pageSize || 10 }));
                  },
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `Hiển thị ${range[0]}-${range[1]} trong tổng số ${total} đơn hàng`,
                }}
                scroll={{ x: 1200 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span
                          style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}
                        >
                          Không có dữ liệu
                        </span>
                      }
                    />
                  ),
                }}
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
              />
            </StyledTable>
          </CardContent>
        </Card>
      </Spin>

      {/* Export Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Download size={16} style={{ color: '#059669' }} />
            <span>Xuất Excel đơn hàng</span>
          </div>
        }
        open={exportModalVisible}
        onCancel={() => {
          setExportModalVisible(false);
          setExportDateRange(null);
          setExportStatuses([]);
        }}
        onOk={handleExportConfirm}
        confirmLoading={exporting}
        okText="Xuất Excel"
        cancelText="Hủy"
        okButtonProps={{
          style: { background: '#059669', borderColor: '#059669' },
        }}
        width={600}
      >
        <div style={{ marginBottom: 24 }}>
          <div style={{ fontWeight: 500, marginBottom: 12, color: '#1e293b' }}>
            Chọn khoảng thời gian (tùy chọn):
          </div>
          <RangePicker
            value={exportDateRange}
            onChange={setExportDateRange}
            style={{ width: '100%' }}
            size="large"
            placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
            format="DD/MM/YYYY"
          />
        </div>

        <div style={{ marginBottom: 24 }}>
          <div style={{ fontWeight: 500, marginBottom: 12, color: '#1e293b' }}>
            Chọn trạng thái đơn hàng:
          </div>
          <Checkbox.Group
            value={exportStatuses}
            onChange={setExportStatuses}
            style={{ width: '100%' }}
          >
            <Row gutter={[16, 12]}>
              {statusOptions
                .filter((option) => option.value !== '')
                .map((option) => (
                  <Col span={12} key={option.value}>
                    <Checkbox value={option.value}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        {option.icon}
                        <span>{option.label}</span>
                      </div>
                    </Checkbox>
                  </Col>
                ))}
            </Row>
          </Checkbox.Group>
        </div>
      </Modal>

      {/* Order Detail Modal */}
      <Modal
        title={
          <span
            style={{
              fontWeight: 500,
              fontSize: 16,
              color: '#344054',
              fontFamily: "'Be Vietnam Pro', sans-serif",
            }}
          >
            Chi tiết đơn hàng: {selectedOrder?.code || ''}
          </span>
        }
        open={orderDetailVisible}
        onCancel={() => {
          setOrderDetailVisible(false);
          setSelectedOrder(null);
        }}
        footer={
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 12 }}>
            <AntButton
              onClick={() => {
                setOrderDetailVisible(false);
                setSelectedOrder(null);
              }}
              style={{
                borderRadius: '6px',
                fontWeight: 500,
                fontFamily: "'Be Vietnam Pro', sans-serif",
              }}
            >
              Đóng
            </AntButton>
            <AntButton
              type="primary"
              icon={<Printer size={16} />}
              onClick={handlePrintInvoice}
              style={{
                borderRadius: '6px',
                fontWeight: 500,
                fontFamily: "'Be Vietnam Pro', sans-serif",
                background: '#2563eb',
                borderColor: '#2563eb',
              }}
            >
              In hóa đơn
            </AntButton>
          </div>
        }
        width="90%"
        style={{
          maxWidth: '1200px',
          top: 0,
          paddingBottom: 0,
        }}
        styles={{
          header: {
            borderBottom: '1px solid #e2e8f0',
            paddingBottom: '16px',
            fontFamily: "'Be Vietnam Pro', sans-serif",
          },
          body: {
            paddingTop: '24px',
            maxHeight: 'calc(100vh - 120px)',
            overflowY: 'auto',
            fontFamily: "'Be Vietnam Pro', sans-serif",
          },
        }}
      >
        <Spin spinning={orderDetailLoading} tip="Đang tải chi tiết đơn hàng...">
          {selectedOrder && (
            <div className="order-details" style={{ width: '100%', overflow: 'hidden' }}>
              {/* Order Progress Steps */}
              <div
                style={{
                  background: '#f8fafc',
                  borderRadius: 12,
                  padding: 24,
                  marginBottom: 24,
                  border: '1px solid #e2e8f0',
                }}
              >
                <div style={{ position: 'relative' }}>
                  {/* Steps */}
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      width: '100%',
                      position: 'relative',
                    }}
                  >
                    {getOrderSteps(selectedOrder.statusOrder).map((step, index) => (
                      <div
                        key={step.status}
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          flex: 1,
                          position: 'relative',
                          zIndex: 2,
                        }}
                      >
                        {/* Step Icon */}
                        <div
                          style={{
                            width: 48,
                            height: 48,
                            borderRadius: 12,
                            background: step.isActive
                              ? `linear-gradient(135deg, ${step.color} 0%, ${step.color}dd 100%)`
                              : step.stepStatus === 'finish'
                                ? '#10b981'
                                : '#e2e8f0',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color:
                              step.isActive || step.stepStatus === 'finish' ? 'white' : '#64748b',
                            marginBottom: 12,
                            boxShadow: step.isActive
                              ? `0 4px 12px ${step.color}40`
                              : step.stepStatus === 'finish'
                                ? '0 4px 12px rgba(16, 185, 129, 0.3)'
                                : 'none',
                            transition: 'all 0.3s ease',
                          }}
                        >
                          {step.stepStatus === 'finish' ? <CheckCircle2 size={20} /> : step.icon}
                        </div>

                        {/* Step Content */}
                        <div style={{ textAlign: 'center', maxWidth: 120 }}>
                          <div
                            style={{
                              fontSize: 14,
                              fontWeight: 600,
                              color: step.isActive
                                ? step.color
                                : step.stepStatus === 'finish'
                                  ? '#10b981'
                                  : '#64748b',
                              marginBottom: 4,
                            }}
                          >
                            {step.title}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#64748b',
                              lineHeight: 1.3,
                            }}
                          >
                            {step.description}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Progress Line */}
                  <div
                    style={{
                      position: 'absolute',
                      top: 24,
                      left: '12.5%',
                      right: '12.5%',
                      height: 4,
                      background: '#e2e8f0',
                      borderRadius: 2,
                      zIndex: 1,
                    }}
                  >
                    <div
                      style={{
                        height: '100%',
                        background: 'linear-gradient(90deg, #10b981 0%, #059669 100%)',
                        borderRadius: 2,
                        width: `${(getOrderSteps(selectedOrder.statusOrder).filter((s) => s.stepStatus === 'finish').length / Math.max(getOrderSteps(selectedOrder.statusOrder).length - 1, 1)) * 100}%`,
                        transition: 'width 0.5s ease',
                      }}
                    />
                  </div>
                </div>
              </div>

              <Row gutter={[16, 16]} style={{ margin: 0, width: '100%' }}>
                {/* Order Information */}
                <Col xs={24} lg={12} style={{ paddingLeft: 8, paddingRight: 8 }}>
                  <AntCard
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <span>Thông tin đơn hàng</span>
                      </div>
                    }
                    size="small"
                    style={{ height: '100%' }}
                  >
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          flexWrap: 'wrap',
                          gap: 8,
                        }}
                      >
                        <span style={{ color: '#64748b', minWidth: 'fit-content' }}>
                          Mã đơn hàng:
                        </span>
                        <span
                          style={{
                            fontWeight: 500,
                            color: '#2563eb',
                            wordBreak: 'break-word',
                            textAlign: 'right',
                          }}
                        >
                          {selectedOrder.code}
                        </span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ color: '#64748b' }}>Trạng thái:</span>
                        <Tag
                          color={getStatusConfig(selectedOrder.statusOrder).color}
                          style={{
                            margin: 0,
                            padding: '4px 8px',
                            borderRadius: 6,
                            fontWeight: 500,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 4,
                            width: 'fit-content',
                          }}
                        >
                          {getStatusConfig(selectedOrder.statusOrder).icon}
                          {selectedOrder.statusOrder}
                        </Tag>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ color: '#64748b' }}>Tiền hàng:</span>
                        <span style={{ fontWeight: 500, color: '#374151' }}>
                          {formatCurrency(
                            selectedOrder.priceBeforeTax ||
                              selectedOrder.priceAfterTax - selectedOrder.taxAmount
                          )}
                        </span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ color: '#64748b' }}>Thuế:</span>
                        <span style={{ fontWeight: 500 }}>
                          {formatCurrency(selectedOrder.taxAmount || 0)}
                        </span>
                      </div>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          paddingTop: '8px',
                          borderTop: '1px solid #e2e8f0',
                          marginTop: '8px',
                        }}
                      >
                        <span style={{ color: '#64748b', fontWeight: 500 }}>Tổng tiền:</span>
                        <span style={{ fontWeight: 600, fontSize: '16px' }}>
                          {formatCurrency(selectedOrder.priceAfterTax)}
                        </span>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span style={{ color: '#64748b' }}>Ngày tạo:</span>
                        <span style={{ fontWeight: 500 }}>
                          {dayjs(selectedOrder.createdAt).format('DD/MM/YYYY HH:mm')}
                        </span>
                      </div>
                    </div>
                  </AntCard>
                </Col>

                {/* Customer Information */}
                <Col xs={24} lg={12} style={{ paddingLeft: 8, paddingRight: 8 }}>
                  <AntCard
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <span>Thông tin khách hàng</span>
                      </div>
                    }
                    size="small"
                    style={{ height: '100%' }}
                  >
                    {selectedOrder.customer ? (
                      <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span style={{ color: '#64748b' }}>Tên:</span>
                          <span style={{ fontWeight: 500, color: '#374151' }}>
                            {selectedOrder.customer.name}
                          </span>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span style={{ color: '#64748b' }}>Điện thoại:</span>
                          <span style={{ fontWeight: 500, color: '#374151' }}>
                            {selectedOrder.customer.phone}
                          </span>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span style={{ color: '#64748b' }}>Email:</span>
                          <span style={{ fontWeight: 500, color: '#374151' }}>
                            {selectedOrder.customer.email || 'N/A'}
                          </span>
                        </div>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'flex-start',
                            flexWrap: 'wrap',
                            gap: 8,
                          }}
                        >
                          <span style={{ color: '#64748b', minWidth: 'fit-content' }}>
                            Địa chỉ:
                          </span>
                          <span
                            style={{
                              fontWeight: 500,
                              color: '#374151',
                              textAlign: 'right',
                              flex: 1,
                              minWidth: 0,
                              wordBreak: 'break-word',
                              lineHeight: 1.4,
                            }}
                          >
                            {[
                              selectedOrder.customer.address,
                              selectedOrder.customer.ward,
                              selectedOrder.customer.district,
                              selectedOrder.customer.city,
                            ]
                              .filter(Boolean)
                              .join(', ')}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <div style={{ textAlign: 'center', color: '#64748b', padding: '20px 0' }}>
                        Không có thông tin khách hàng
                      </div>
                    )}
                  </AntCard>
                </Col>

                {/* Products Information */}
                <Col xs={24} style={{ paddingLeft: 8, paddingRight: 8 }}>
                  <AntCard
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <span>Danh sách sản phẩm</span>
                      </div>
                    }
                    size="small"
                  >
                    {selectedOrder.products && selectedOrder.products.length > 0 ? (
                      <div style={{ overflowX: 'auto', width: '100%' }}>
                        <AntTable
                          dataSource={selectedOrder.products}
                          pagination={false}
                          size="small"
                          rowKey={(_, index) => index || 0}
                          scroll={{ x: 'max-content' }}
                          columns={[
                            {
                              title: 'Tên sản phẩm',
                              dataIndex: 'name',
                              key: 'name',
                              ellipsis: true,
                              render: (name: string) => (
                                <span style={{ fontWeight: 500, color: '#374151' }}>{name}</span>
                              ),
                            },
                            {
                              title: 'SL',
                              dataIndex: 'quantity',
                              key: 'quantity',
                              width: 60,
                              align: 'center',
                              render: (quantity: number) => (
                                <span style={{ fontWeight: 500 }}>{quantity}</span>
                              ),
                            },
                            {
                              title: 'Đơn giá',
                              dataIndex: 'price',
                              key: 'price',
                              width: 120,
                              align: 'right',
                              render: (price: number) => (
                                <span
                                  style={{ fontWeight: 500, color: '#374151', fontSize: '13px' }}
                                >
                                  {formatCurrency(price)}
                                </span>
                              ),
                            },
                            {
                              title: 'Thành tiền',
                              key: 'total',
                              width: 120,
                              align: 'right',
                              render: (_: any, record: any) => (
                                <span style={{ fontWeight: 500, fontSize: '13px' }}>
                                  {formatCurrency((record.price || 0) * (record.quantity || 0))}
                                </span>
                              ),
                            },
                          ]}
                          style={{
                            fontFamily: "'Be Vietnam Pro', sans-serif",
                          }}
                        />
                      </div>
                    ) : (
                      <div style={{ textAlign: 'center', color: '#64748b', padding: '20px 0' }}>
                        Không có sản phẩm nào
                      </div>
                    )}
                  </AntCard>
                </Col>

                {/* Commission Information */}
                <Col xs={24} style={{ paddingLeft: 8, paddingRight: 8 }}>
                  <AntCard
                    title={
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                          <span style={{ fontWeight: 600 }}>
                            Thông tin hoa hồng({(selectedOrder as any)?.hoa_hongs?.length || 0})
                          </span>
                        </div>
                      </div>
                    }
                    size="small"
                  >
                    {(selectedOrder as any)?.hoa_hongs &&
                    (selectedOrder as any).hoa_hongs.length > 0 ? (
                      <div style={{ overflowX: 'auto', width: '100%' }}>
                        <AntTable
                          dataSource={(selectedOrder as any).hoa_hongs}
                          pagination={false}
                          size="small"
                          rowKey={(record: any, index?: number) =>
                            record.id || index || Math.random()
                          }
                          scroll={{ x: 'max-content' }}
                          columns={[
                            {
                              title: 'STT',
                              key: 'stt',
                              width: 60,
                              align: 'center',
                              render: (_: any, __: any, index: number) => (
                                <span style={{ fontWeight: 600, fontSize: '13px' }}>
                                  {index + 1}
                                </span>
                              ),
                            },
                            {
                              title: 'Người hưởng',
                              key: 'recipient',
                              width: 180,
                              render: (record: any) => (
                                <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
                                  <div
                                    style={{ fontWeight: 500, color: '#374151', fontSize: '13px' }}
                                  >
                                    {record.user?.name || 'N/A'}
                                  </div>
                                  <div style={{ fontSize: '12px', color: '#64748b' }}>
                                    {record.user?.phone || 'N/A'}
                                  </div>
                                </div>
                              ),
                            },
                            {
                              title: 'Số tiền',
                              dataIndex: 'amount',
                              key: 'amount',
                              width: 130,
                              align: 'right',
                              render: (amount: number) => (
                                <span
                                  style={{ fontWeight: 500, color: '#10b981', fontSize: '13px' }}
                                >
                                  {formatCurrency(amount)}
                                </span>
                              ),
                            },
                            {
                              title: 'Tỷ lệ',
                              dataIndex: 'percentage',
                              key: 'percentage',
                              width: 80,
                              align: 'center',
                              render: (percentage: number) => (
                                <span
                                  style={{ fontWeight: 500, color: '#2563eb', fontSize: '13px' }}
                                >
                                  {percentage}%
                                </span>
                              ),
                            },
                            {
                              title: 'Trạng thái',
                              dataIndex: 'statusPaid',
                              key: 'statusPaid',
                              width: 120,
                              align: 'center',
                              render: (statusPaid: string) => (
                                <Tag
                                  color={
                                    statusPaid === 'paid'
                                      ? '#10b981'
                                      : statusPaid === 'cancelled'
                                        ? '#ef4444'
                                        : '#f59e0b'
                                  }
                                  style={{
                                    padding: '4px 12px',
                                    borderRadius: 6,
                                    fontWeight: 500,
                                    fontSize: 12,
                                    border: 'none',
                                    margin: 0,
                                  }}
                                >
                                  {statusPaid === 'paid'
                                    ? 'Đã duyệt'
                                    : statusPaid === 'cancelled'
                                      ? 'Đã hủy'
                                      : 'Chờ duyệt'}
                                </Tag>
                              ),
                            },
                          ]}
                          style={{
                            fontFamily: "'Be Vietnam Pro', sans-serif",
                          }}
                        />
                      </div>
                    ) : (
                      <div
                        style={{
                          textAlign: 'center',
                          color: '#64748b',
                          padding: '60px 20px',
                          background: '#f8fafc',
                          borderRadius: 8,
                          border: '1px dashed #e2e8f0',
                        }}
                      >
                        <div style={{ fontSize: 24, marginBottom: 12 }}>💰</div>
                        <div style={{ fontSize: 16, fontWeight: 500, marginBottom: 4 }}>
                          Không có hoa hồng nào
                        </div>
                        <div style={{ fontSize: 14, color: '#94a3b8' }}>
                          Đơn hàng này chưa có hoa hồng được thiết lập
                        </div>
                      </div>
                    )}
                  </AntCard>
                </Col>
              </Row>
            </div>
          )}
        </Spin>
      </Modal>

      {/* Create Order Drawer */}
      <Drawer
        title={
          <span
            style={{
              fontWeight: 500,
              fontSize: 16,
              color: '#344054',
              fontFamily: "'Be Vietnam Pro', sans-serif",
              display: 'flex',
              alignItems: 'center',
              gap: 8,
            }}
          >
            Tạo đơn hàng mới
          </span>
        }
        placement="right"
        width={'60%'}
        open={createOrderVisible}
        onClose={() => {
          setCreateOrderVisible(false);
          resetCustomerForm();
          setSelectedProducts([]);
          setProductSearchTerm('');
          setTaxPercentage(0);
        }}
        footer={
          <div
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: 12,
              padding: '16px 24px',
              borderTop: '1px solid #e2e8f0',
            }}
          >
            <AntButton
              onClick={() => setCreateOrderVisible(false)}
              disabled={createOrderLoading}
              style={{
                borderRadius: '6px',
                fontWeight: 500,
                fontFamily: "'Be Vietnam Pro', sans-serif",
              }}
            >
              Hủy
            </AntButton>
            <AntButton
              type="primary"
              loading={createOrderLoading}
              icon={<Plus size={16} />}
              onClick={handleCreateOrder}
              style={{
                borderRadius: '6px',
                fontWeight: 500,
                fontFamily: "'Be Vietnam Pro', sans-serif",
                background: '#2563eb',
                borderColor: '#2563eb',
              }}
            >
              Tạo đơn hàng
            </AntButton>
          </div>
        }
        styles={{
          header: {
            borderBottom: '1px solid #e2e8f0',
            paddingBottom: '16px',
            fontFamily: "'Be Vietnam Pro', sans-serif",
          },
          body: {
            paddingTop: '24px',
            paddingBottom: '80px',
            fontFamily: "'Be Vietnam Pro', sans-serif",
          },
        }}
        afterOpenChange={(open) => {
          if (open) {
            fetchUsers();
            fetchProducts();
          }
        }}
      >
        <div style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}>
          <div
            style={{
              marginBottom: 24,
              padding: 16,
              background: '#f8fafc',
              borderRadius: 8,
              border: '1px solid #e2e8f0',
            }}
          >
            <div style={{ fontSize: 14, color: '#64748b', marginBottom: 8 }}>
              📝 Hướng dẫn tạo đơn hàng
            </div>
            <div style={{ fontSize: 13, color: '#64748b', lineHeight: 1.5 }}>
              Điền đầy đủ thông tin khách hàng và sản phẩm để tạo đơn hàng mới. Tất cả các trường có
              dấu (*) là bắt buộc.
            </div>
          </div>

          {/* Customer Information */}
          <div style={{ marginBottom: 24 }}>
            <div
              style={{
                fontSize: 16,
                fontWeight: 600,
                marginBottom: 16,
                color: '#374151',
                display: 'flex',
                alignItems: 'center',
                gap: 8,
              }}
            >
              <div style={{ width: 4, height: 20, background: '#2563eb', borderRadius: 2 }}></div>
              Thông tin khách hàng
            </div>

            {/* User Selection */}
            <div style={{ marginBottom: 16 }}>
              <label
                style={{ display: 'block', marginBottom: 8, fontWeight: 500, color: '#374151' }}
              >
                Chọn từ danh sách đại lý
              </label>
              <Select
                placeholder={
                  users.length === 0 && !usersLoading
                    ? 'Không có đại lý nào trong hệ thống'
                    : 'Chọn đại lý có sẵn hoặc nhập thông tin mới'
                }
                style={{ width: '100%' }}
                size="large"
                loading={usersLoading}
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
                value={selectedUserId}
                onChange={(value) => {
                  if (value) {
                    handleUserSelect(value);
                  } else {
                    resetCustomerForm();
                  }
                }}
                options={users.map((user) => ({
                  value: user.id,
                  label: `${user.name} - ${user.phone}`,
                }))}
                notFoundContent={usersLoading ? 'Đang tải...' : 'Không có dữ liệu'}
              />
            </div>

            <div
              style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16, marginBottom: 16 }}
            >
              <div>
                <label
                  style={{ display: 'block', marginBottom: 8, fontWeight: 500, color: '#374151' }}
                >
                  Tên khách hàng <span style={{ color: '#ef4444' }}>*</span>
                </label>
                <Input
                  placeholder="Nhập tên khách hàng"
                  size="large"
                  value={customerFormData.name}
                  onChange={(e) =>
                    setCustomerFormData({ ...customerFormData, name: e.target.value })
                  }
                />
              </div>
              <div>
                <label
                  style={{ display: 'block', marginBottom: 8, fontWeight: 500, color: '#374151' }}
                >
                  Số điện thoại <span style={{ color: '#ef4444' }}>*</span>
                </label>
                <Input
                  placeholder="Nhập số điện thoại"
                  size="large"
                  value={customerFormData.phone}
                  onChange={(e) =>
                    setCustomerFormData({ ...customerFormData, phone: e.target.value })
                  }
                />
              </div>
            </div>

            <div style={{ marginBottom: 16 }}>
              <label
                style={{ display: 'block', marginBottom: 8, fontWeight: 500, color: '#374151' }}
              >
                Email
              </label>
              <Input
                type="email"
                placeholder="Nhập email khách hàng"
                size="large"
                value={customerFormData.email}
                onChange={(e) =>
                  setCustomerFormData({ ...customerFormData, email: e.target.value })
                }
              />
            </div>

            <div>
              <label
                style={{ display: 'block', marginBottom: 8, fontWeight: 500, color: '#374151' }}
              >
                Địa chỉ <span style={{ color: '#ef4444' }}>*</span>
              </label>
              <Input.TextArea
                placeholder="Nhập địa chỉ đầy đủ"
                rows={3}
                size="large"
                value={customerFormData.address}
                onChange={(e) =>
                  setCustomerFormData({ ...customerFormData, address: e.target.value })
                }
                style={{
                  resize: 'vertical',
                }}
              />
            </div>
          </div>

          {/* Order Information */}
          <div style={{ marginBottom: 24 }}>
            <div
              style={{
                fontSize: 16,
                fontWeight: 600,
                marginBottom: 16,
                color: '#374151',
                display: 'flex',
                alignItems: 'center',
                gap: 8,
              }}
            >
              <div style={{ width: 4, height: 20, background: '#059669', borderRadius: 2 }}></div>
              Thông tin đơn hàng
            </div>

            {/* Product Selection */}
            <div style={{ marginBottom: 16 }}>
              <label
                style={{ display: 'block', marginBottom: 8, fontWeight: 500, color: '#374151' }}
              >
                Chọn sản phẩm <span style={{ color: '#ef4444' }}>*</span>
              </label>
              <AutoComplete
                placeholder="Tìm và chọn sản phẩm"
                style={{ width: '100%' }}
                size="large"
                value={productSearchTerm}
                onSearch={(value) => {
                  setProductSearchTerm(value);
                  const sanitizedValue = sanitizeSearchTerm(value);

                  // Always search when value changes (including when deleting characters)
                  if (sanitizedValue.length >= 1 || value.length === 0) {
                    fetchProducts(value);
                  } else if (value.length > 0 && sanitizedValue.length === 0) {
                    // If original value has content but sanitized is empty (all special chars)
                    message.warning('Vui lòng nhập từ khóa hợp lệ để tìm kiếm');
                    setProducts([]);
                  }
                }}
                onChange={(value) => {
                  setProductSearchTerm(value);
                  const sanitizedValue = sanitizeSearchTerm(value);

                  // Also trigger search on onChange (when user deletes characters)
                  if (sanitizedValue.length >= 1 || value.length === 0) {
                    fetchProducts(value);
                  } else if (value.length > 0 && sanitizedValue.length === 0) {
                    setProducts([]);
                  }
                }}
                onSelect={() => {
                  // Disable default select behavior since we use the Add button
                }}
                notFoundContent={
                  productsLoading ? <Spin size="small" /> : 'Không tìm thấy sản phẩm'
                }
                options={products.map((product) => {
                  const imageUrl =
                    product.hinh_anh && product.hinh_anh.length > 0
                      ? product.hinh_anh[0]?.formats?.thumbnail?.url ||
                        product.hinh_anh[0]?.formats?.small?.url ||
                        product.hinh_anh[0]?.url
                      : null;

                  return {
                    value: product.id,
                    label: (
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 12,
                          padding: '8px 4px',
                          cursor: 'default',
                        }}
                        onClick={(e) => e.preventDefault()}
                      >
                        {/* Product Image */}
                        <div
                          style={{
                            width: 48,
                            height: 48,
                            borderRadius: 8,
                            overflow: 'hidden',
                            border: '1px solid #e2e8f0',
                            backgroundColor: '#f8fafc',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexShrink: 0,
                          }}
                        >
                          {imageUrl ? (
                            <img
                              src={imageUrl}
                              alt={product.name}
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover',
                              }}
                              onError={(e) => {
                                e.currentTarget.style.display = 'none';
                                if (e.currentTarget.parentElement) {
                                  e.currentTarget.parentElement.innerHTML = '📦';
                                }
                              }}
                            />
                          ) : (
                            <span style={{ fontSize: '20px', color: '#94a3b8' }}>📦</span>
                          )}
                        </div>

                        {/* Product Info */}
                        <div style={{ flex: 1, minWidth: 0 }}>
                          <div
                            style={{
                              fontWeight: 500,
                              color: '#374151',
                              fontSize: '14px',
                              marginBottom: 4,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {product.name}
                          </div>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 8,
                              fontSize: '13px',
                            }}
                          >
                            <span
                              style={{
                                color: '#059669',
                                fontWeight: 600,
                              }}
                            >
                              {formatCurrency(product.gia_ban || 0)}
                            </span>
                            {product.so_luong_ton_kho !== undefined && (
                              <span
                                style={{
                                  color: product.so_luong_ton_kho > 0 ? '#64748b' : '#ef4444',
                                  fontSize: '12px',
                                }}
                              >
                                Kho: {product.so_luong_ton_kho}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Add Button */}
                        <AntButton
                          type="primary"
                          size="small"
                          icon={<Plus size={14} />}
                          style={{
                            borderRadius: 6,
                            height: 32,
                            width: 32,
                            padding: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            background: '#2563eb',
                            borderColor: '#2563eb',
                            flexShrink: 0,
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleProductSelect(product.id, 1);
                            setProductSearchTerm('');
                          }}
                        />
                      </div>
                    ),
                  };
                })}
              />
            </div>

            {/* Selected Products List */}
            {selectedProducts.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <label
                  style={{ display: 'block', marginBottom: 8, fontWeight: 500, color: '#374151' }}
                >
                  Sản phẩm đã chọn
                </label>
                <div
                  style={{
                    border: '1px solid #e2e8f0',
                    borderRadius: 8,
                    overflow: 'hidden',
                  }}
                >
                  {selectedProducts.map((product, index) => (
                    <div
                      key={product.id}
                      style={{
                        padding: 12,
                        borderBottom:
                          index < selectedProducts.length - 1 ? '1px solid #e2e8f0' : 'none',
                        background: '#f8fafc',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 12,
                      }}
                    >
                      {/* Product Image */}
                      <div
                        style={{
                          width: 48,
                          height: 48,
                          borderRadius: 8,
                          overflow: 'hidden',
                          border: '1px solid #e2e8f0',
                          backgroundColor: '#ffffff',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0,
                        }}
                      >
                        {product.imageUrl ? (
                          <img
                            src={product.imageUrl}
                            alt={product.name}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                            }}
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              if (e.currentTarget.parentElement) {
                                e.currentTarget.parentElement.innerHTML = '📦';
                              }
                            }}
                          />
                        ) : (
                          <span style={{ fontSize: '20px', color: '#94a3b8' }}>📦</span>
                        )}
                      </div>

                      {/* Product Info */}
                      <div style={{ flex: 1, minWidth: 0 }}>
                        <div
                          style={{
                            fontWeight: 500,
                            color: '#374151',
                            marginBottom: 4,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {product.name}
                        </div>
                        <div style={{ fontSize: 13, color: '#64748b' }}>
                          {formatCurrency(product.price)} x {product.quantity} ={' '}
                          <span style={{ fontWeight: 600, color: '#059669' }}>
                            {formatCurrency(product.total)}
                          </span>
                        </div>
                      </div>

                      {/* Controls */}
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8, flexShrink: 0 }}>
                        <Input
                          type="number"
                          min="1"
                          value={product.quantity}
                          onChange={(e) => {
                            const newQuantity = parseInt(e.target.value) || 1;
                            handleProductSelect(product.id, newQuantity);
                          }}
                          style={{ width: 80 }}
                          size="small"
                        />
                        <AntButton
                          type="text"
                          danger
                          size="small"
                          icon={<X size={14} />}
                          onClick={() => handleRemoveProduct(product.id)}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div
              style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: 16, marginBottom: 16 }}
            >
              <div>
                <label
                  style={{ display: 'block', marginBottom: 8, fontWeight: 500, color: '#374151' }}
                >
                  Ghi chú đơn hàng
                </label>
                <textarea
                  placeholder="Nhập ghi chú cho đơn hàng (tùy chọn)"
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                    resize: 'vertical',
                  }}
                />
              </div>
              <div>
                <label
                  style={{ display: 'block', marginBottom: 8, fontWeight: 500, color: '#374151' }}
                >
                  Thuế (%)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  placeholder="0"
                  size="large"
                  value={taxPercentage}
                  onChange={(e) => {
                    const value = parseFloat(e.target.value) || 0;
                    if (value >= 0 && value <= 100) {
                      setTaxPercentage(value);
                    }
                  }}
                  suffix="%"
                  style={{
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                />
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div
            style={{
              marginBottom: 24,
              padding: 16,
              background: '#f8fafc',
              borderRadius: 8,
              border: '1px solid #e2e8f0',
            }}
          >
            <div style={{ fontSize: 16, fontWeight: 600, marginBottom: 12, color: '#374151' }}>
              Tóm tắt đơn hàng
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
              <span style={{ color: '#64748b' }}>Tạm tính:</span>
              <span style={{ fontWeight: 500 }}>
                {formatCurrency(calculateOrderTotals().subtotal)}
              </span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
              <span style={{ color: '#64748b' }}>Thuế ({taxPercentage}%):</span>
              <span style={{ fontWeight: 500 }}>{formatCurrency(calculateOrderTotals().tax)}</span>
            </div>
            <div style={{ height: '1px', background: '#e2e8f0', margin: '12px 0' }}></div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ fontSize: 16, fontWeight: 600, color: '#374151' }}>Tổng cộng:</span>
              <span style={{ fontSize: 16, fontWeight: 600, color: '#059669' }}>
                {formatCurrency(calculateOrderTotals().total)}
              </span>
            </div>
          </div>
        </div>
      </Drawer>
    </PageContainer>
  );
};

export default OrderManagement;
