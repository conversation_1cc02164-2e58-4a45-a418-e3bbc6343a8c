import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table as AntTable,
  Button as AntButton,
  Input,
  Select,
  Space,
  Tag,
  Avatar,
  Typography,
  Row,
  Col,
  Tooltip,
  message,
  Modal,
  Switch,
  Badge,
  Tabs,
  Empty,
  Pagination,
  Statistic,
  Spin,
} from 'antd';
import {
  UserOutlined,
  SearchOutlined,
  FileExcelOutlined,
  EyeOutlined,
  EditOutlined,
  StopOutlined,
  CheckCircleOutlined,
  TeamOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { Users, UserCheck, UserX, Download, Filter } from 'lucide-react';
import * as XLSX from 'xlsx';
import { useFetchClient } from '@strapi/helper-plugin';
import UserCreateModal from '../components/UserCreateModal';
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
  FilterGroup,
  FilterLabel,
  SelectInput,
  ActionButtonGroup,
  StyledTable,
} from '../components/shared';
const { Title, Paragraph } = Typography;

// Status options for user management
const statusOptions = [
  { value: '', label: 'Tất cả', color: '#666', icon: null },
  { value: 'active', label: 'Hoạt động', color: '#52c41a', icon: <CheckCircleOutlined /> },
  { value: 'blocked', label: 'Đã khóa', color: '#ff4d4f', icon: <StopOutlined /> },
];

// Tab items for status filtering
const getTabItems = (statistics: Statistics | null) => {
  const getCountByStatus = (status: string) => {
    if (status === '') return statistics?.totalUsers || 0;
    if (status === 'active') return statistics?.activeUsers || 0;
    if (status === 'blocked') return statistics?.blockedUsers || 0;
    return 0;
  };

  // Statuses that should show badge counts
  const statusesWithBadge = ['blocked'];

  return statusOptions.map((option) => ({
    key: option.value,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: 0 }}>
        {option.icon}
        <span style={{ marginRight: 6 }}>{option.label}</span>
        {statusesWithBadge.includes(option.value) && (
          <Badge
            count={getCountByStatus(option.value)}
            style={{
              backgroundColor: option.color,
              fontSize: '12px',
              width: '20px',
              height: '20px',
            }}
          />
        )}
      </div>
    ),
  }));
};

interface User {
  id: number;
  username: string;
  avatar: string;
  balance: number;
  email: string;
  name: string;
  phone: string;
  blocked: boolean;
  confirmed: boolean;
  createdAt: string;
  referUser?: {
    id: number;
    name: string;
    username: string;
    phone: string;
  };
  role: {
    id: number;
    name: string;
    type: string;
  };
}

interface UserListResponse {
  data: User[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

interface Statistics {
  totalUsers: number;
  activeUsers: number;
  blockedUsers: number;
  newUsersThisMonth: number;
  usersByRole: Array<{ role: string; count: number }>;
}

export const UserList: React.FC = () => {
  const navigate = useNavigate();
  const { get, put } = useFetchClient();

  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [status, setStatus] = useState('');
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);

  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userDetailVisible, setUserDetailVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);

  const PAGE_SIZE = 10;

  // Stats data for display
  const statsData = [
    {
      title: 'Tổng đại lý',
      value: statistics?.totalUsers || 0,
      icon: Users,
      color: '#3b82f6',
    },
    {
      title: 'Đang hoạt động',
      value: statistics?.activeUsers || 0,
      icon: UserCheck,
      color: '#10b981',
    },
    {
      title: 'Đã khóa',
      value: statistics?.blockedUsers || 0,
      icon: UserX,
      color: '#ef4444',
    },
    {
      title: 'Mới tháng này',
      value: statistics?.newUsersThisMonth || 0,
      icon: TeamOutlined,
      color: '#8b5cf6',
    },
  ];

  const fetchUsers = async (currentPage = page, currentSearch = search, currentStatus = status) => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: PAGE_SIZE.toString(),
        ...(currentSearch && { search: currentSearch }),
        ...(currentStatus === 'active' && { blocked: 'false' }),
        ...(currentStatus === 'blocked' && { blocked: 'true' }),
      });

      const response = await get<UserListResponse>(`/management/users?${queryParams}`);

      setUsers(response.data.data);
      setTotal(response.data.meta.pagination.total);
    } catch (error) {
      console.error('Error fetching users:', error);
      message.error('Không thể tải danh sách đại lý');
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  // Handle filter change
  const handleFilterChange = (filterType: string, value: string) => {
    if (filterType === 'status') {
      setStatus(value);
    }
    setPage(1);
  };

  // Create and download Excel file
  const createAndDownloadExcel = (users: User[]) => {
    if (!users.length) {
      message.warning('Không có dữ liệu để xuất');
      return;
    }

    // Prepare Excel data
    const excelData = users.map((user, index) => ({
      STT: index + 1,
      ID: user.id,
      'Tên đại lý': user.name || 'N/A',
      'Tên đăng nhập': user.username || 'N/A',
      Email: user.email || 'N/A',
      'Số điện thoại': user.phone || 'N/A',
      'Số dư': user.balance
        ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
            user.balance
          )
        : '0 ₫',
      'Vai trò': user.role?.name || 'N/A',
      'Trạng thái': user.blocked ? 'Đã khóa' : 'Hoạt động',
      'Người giới thiệu': user.referUser?.name || 'N/A',
      'Ngày tạo': new Date(user.createdAt).toLocaleString('vi-VN'),
    }));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths
    const columnWidths = [
      { wch: 5 }, // STT
      { wch: 8 }, // ID
      { wch: 25 }, // Tên đại lý
      { wch: 20 }, // Tên đăng nhập
      { wch: 30 }, // Email
      { wch: 15 }, // Số điện thoại
      { wch: 20 }, // Số dư
      { wch: 15 }, // Vai trò
      { wch: 15 }, // Trạng thái
      { wch: 25 }, // Người giới thiệu
      { wch: 20 }, // Ngày tạo
    ];
    worksheet['!cols'] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Danh sách đại lý');

    // Generate filename with current date
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0];
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
    const filename = `danh-sach-dai-ly-${dateStr}-${timeStr}.xlsx`;

    // Write and download file
    XLSX.writeFile(workbook, filename);
  };

  // Handle export all users
  const handleBulkExport = async () => {
    try {
      // Prepare query params for export (same as current filters)
      const queryParams = new URLSearchParams({
        page: '1',
        pageSize: '10000', // Get all users
        ...(search && { search }),
        ...(status === 'active' && { blocked: 'false' }),
        ...(status === 'blocked' && { blocked: 'true' }),
      });

      // Fetch all users with current filters
      const response = await get<UserListResponse>(`/management/users?${queryParams}`);
      const usersToExport = response.data.data || [];

      if (usersToExport.length === 0) {
        message.warning('Không có đại lý nào phù hợp với bộ lọc đã chọn');
        return;
      }

      // Create and download Excel file
      createAndDownloadExcel(usersToExport);
      message.success(`Đã tải xuống file Excel với ${usersToExport.length} đại lý!`);
    } catch (error) {
      console.error('Error exporting users:', error);
      message.error('Không thể xuất file Excel');
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await get('/management/users/statistics');
      console.log('User statistics response:', response); // Debug log
      // The backend now returns { success: true, data: { ... } }
      setStatistics(response.data?.data || null);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleStatusChange = async (userId: number, blocked: boolean) => {
    try {
      await put(`/management/users/${userId}/status`, { blocked });
      message.success(blocked ? 'Đã khóa tài khoản' : 'Đã mở khóa tài khoản');
      fetchUsers();
      fetchStatistics();
    } catch (error) {
      console.error('Error updating user status:', error);
      message.error('Không thể cập nhật trạng thái');
    }
  };

  // Handle user detail click
  const handleUserClick = async (user: User) => {
    setSelectedUser(user);
    setUserDetailVisible(true);
  };

  useEffect(() => {
    fetchUsers();
    fetchStatistics();
  }, [page, search, status]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (search !== '') {
        fetchUsers(1, search, status);
        setPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  const columns = [
    {
      title: 'Đại lý',
      key: 'user',
      width: 280,
      render: (record: User) => (
        <Space>
          <Avatar
            src={record.avatar}
            size={48}
            style={{
              backgroundColor: '#f0f2f5',
              color: '#666',
            }}
          >
            {record.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <div>
            <div
              style={{
                fontWeight: 600,
                color: '#1e293b',
                fontSize: '14px',
                cursor: 'pointer',
                transition: 'color 0.2s ease',
              }}
              onClick={() => handleUserClick(record)}
              onMouseEnter={(e) => ((e.target as HTMLElement).style.color = '#2563eb')}
              onMouseLeave={(e) => ((e.target as HTMLElement).style.color = '#1e293b')}
            >
              {record.name}
            </div>
            <div style={{ fontSize: '12px', color: '#64748b' }}>{record.phone}</div>
            <div style={{ fontSize: '12px', color: '#94a3b8' }}>{record.email}</div>
          </div>
        </Space>
      ),
      sorter: (a: User, b: User) => a.name.localeCompare(b.name),
    },
    {
      title: 'Người giới thiệu',
      key: 'referUser',
      width: 180,
      render: (record: User) => {
        if (record.referUser) {
          return (
            <div>
              <div style={{ fontWeight: 500, color: '#1e293b', fontSize: '13px' }}>
                {record.referUser.name}
              </div>
              <div style={{ fontSize: '12px', color: '#64748b' }}>{record.referUser.phone}</div>
            </div>
          );
        }
        return (
          <div style={{ color: '#94a3b8', fontSize: '13px', fontStyle: 'italic' }}>Không có</div>
        );
      },
    },
    {
      title: 'Số dư',
      dataIndex: 'balance',
      key: 'balance',
      width: 120,
      render: (value: number) => (
        <div style={{ color: '#059669', fontSize: '13px', fontWeight: 600 }}>
          {value
            ? new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
              }).format(value)
            : '0 ₫'}
        </div>
      ),
    },
    {
      title: 'Vai trò',
      key: 'role',
      width: 120,
      render: (record: User) => (
        <Tag
          color="blue"
          style={{
            margin: 0,
            padding: '4px 8px',
            borderRadius: 6,
            fontWeight: 500,
            fontSize: '12px',
          }}
        >
          {record.role?.name || 'N/A'}
        </Tag>
      ),
    },
    {
      title: 'Trạng thái',
      key: 'status',
      width: 160,
      render: (record: User) => (
        <Space>
          <Tag
            color={record.blocked ? '#ff4d4f' : '#52c41a'}
            icon={record.blocked ? <StopOutlined /> : <CheckCircleOutlined />}
            style={{
              margin: 0,
              padding: '3px 6px',
              borderRadius: 4,
              fontWeight: 500,
              fontSize: '13px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 3,
              width: 'fit-content',
            }}
          >
            {record.blocked ? 'Đã khóa' : 'Hoạt động'}
          </Tag>
        </Space>
      ),
    },
    {
      title: 'Ngày tham gia',
      key: 'createdAt',
      width: 120,
      render: (record: User) => (
        <div style={{ color: '#64748b', fontSize: '13px' }}>
          {new Date(record.createdAt).toLocaleDateString('vi-VN')}
        </div>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (record: User) => (
        <ActionButtonGroup
          onView={() => handleUserClick(record)}
          showView={true}
          showEdit={false}
          showDelete={false}
          viewTooltip="Xem chi tiết đại lý"
          customActions={
            <Tooltip title={record.blocked ? 'Mở khóa tài khoản' : 'Khóa tài khoản'}>
              <AntButton
                type="text"
                icon={record.blocked ? <CheckCircleOutlined /> : <StopOutlined />}
                onClick={() => handleStatusChange(record.id, !record.blocked)}
                style={{
                  color: record.blocked ? '#52c41a' : '#ff4d4f',
                  borderRadius: 6,
                  width: 32,
                  height: 32,
                  padding: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              />
            </Tooltip>
          }
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        {/* Stats Cards */}
        <StatsGrid>
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={<stat.icon />}
              color={stat.color}
            />
          ))}
        </StatsGrid>

        {/* User Management */}
        <Card>
          <PageHeader
            title="Danh sách đại lý"
            description="Xem và quản lý danh sách đại lý"
            actions={
              <Space>
                <Button onClick={handleBulkExport} $variant="outline">
                  <Download />
                  Xuất Excel
                </Button>
                <Button
                  onClick={() => {
                    fetchUsers();
                    fetchStatistics();
                  }}
                  $variant="outline"
                >
                  <ReloadOutlined />
                  Làm mới
                </Button>
                <Button onClick={() => setCreateModalVisible(true)} $variant="primary">
                  <UserAddOutlined />
                  Thêm đại lý
                </Button>
              </Space>
            }
          />

          <CardContent>
            {/* Filters Section */}
            <FiltersSection>
              <SearchBar
                placeholder="Tìm kiếm theo tên, email, số điện thoại..."
                value={search}
                onChange={handleSearch}
              />

              <FilterGroup>
                <FilterLabel>Trạng thái:</FilterLabel>
                <SelectInput
                  value={status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <option value="">Tất cả</option>
                  <option value="active">Hoạt động</option>
                  <option value="blocked">Đã khóa</option>
                </SelectInput>
              </FilterGroup>
            </FiltersSection>

            {/* Users Table */}
            <StyledTable>
              <AntTable
                columns={columns}
                dataSource={users}
                rowKey="id"
                loading={loading}
                pagination={false}
                scroll={{ x: 1200 }}
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span
                          style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}
                        >
                          Không có dữ liệu
                        </span>
                      }
                    />
                  ),
                }}
                style={{
                  fontFamily: "'Be Vietnam Pro', sans-serif",
                }}
              />
            </StyledTable>

            {/* Pagination */}
            {total > 0 && (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: 16,
                  padding: '16px 0',
                  borderTop: '1px solid #f0f0f0',
                }}
              >
                <span style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}>
                  Hiển thị {(page - 1) * PAGE_SIZE + 1} - {Math.min(page * PAGE_SIZE, total)} của{' '}
                  {total} đại lý
                </span>
                <Pagination
                  current={page}
                  pageSize={PAGE_SIZE}
                  total={total}
                  onChange={(newPage) => {
                    setPage(newPage);
                    fetchUsers(newPage, search, status);
                  }}
                  showSizeChanger={false}
                  style={{
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                />
              </div>
            )}
          </CardContent>
        </Card>
      </Spin>

      {/* User Detail Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 20 }}>
            <span style={{ fontWeight: 500, fontSize: 16, color: '#344054' }}>
              Chi tiết đại lý: {selectedUser?.name || ''}
            </span>
          </div>
        }
        open={userDetailVisible}
        onCancel={() => {
          setUserDetailVisible(false);
          setSelectedUser(null);
        }}
        footer={[
          <AntButton key="cancel" onClick={() => setUserDetailVisible(false)}>
            Đóng
          </AntButton>,
        ]}
        width={1200}
        style={{ top: 20 }}
        styles={{
          header: {
            borderBottom: '1px solid #e2e8f0',
            paddingBottom: '16px',
            marginBottom: '0',
            marginLeft: '-24px',
            marginRight: '-24px',
            paddingLeft: '24px',
            paddingRight: '24px',
          },
          body: {
            paddingTop: '24px',
          },
        }}
      >
        {selectedUser && (
          <div style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}>
            <Row gutter={[24, 24]}>
              {/* User Avatar */}
              <Col xs={24} sm={8} md={6}>
                <div style={{ marginBottom: 16 }}>
                  <span style={{ fontWeight: 500, fontSize: 15, color: '#344054' }}>
                    Ảnh đại diện
                  </span>
                </div>
                <div style={{ textAlign: 'center' }}>
                  <Avatar
                    src={selectedUser.avatar}
                    size={120}
                    style={{
                      backgroundColor: '#f0f2f5',
                      color: '#666',
                      fontSize: '48px',
                    }}
                  >
                    {selectedUser.name?.charAt(0)?.toUpperCase()}
                  </Avatar>
                </div>
              </Col>

              {/* User Details */}
              <Col xs={24} sm={16} md={18}>
                <div style={{ marginBottom: 16 }}>
                  <span style={{ fontWeight: 500, fontSize: 15, color: '#344054' }}>
                    Thông tin đại lý
                  </span>
                </div>
                <div
                  style={{
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    overflow: 'hidden',
                  }}
                >
                  <table
                    style={{
                      width: '100%',
                      borderCollapse: 'collapse',
                      fontSize: 14,
                    }}
                  >
                    <tbody>
                      <tr>
                        <td
                          style={{
                            padding: '12px 16px',
                            backgroundColor: '#f8fafc',
                            borderBottom: '1px solid #e2e8f0',
                            fontWeight: 500,
                            color: '#374151',
                            width: '30%',
                          }}
                        >
                          ID
                        </td>
                        <td
                          style={{
                            padding: '12px 16px',
                            borderBottom: '1px solid #e2e8f0',
                            color: '#1f2937',
                          }}
                        >
                          {selectedUser.id}
                        </td>
                      </tr>
                      <tr>
                        <td
                          style={{
                            padding: '12px 16px',
                            backgroundColor: '#f8fafc',
                            borderBottom: '1px solid #e2e8f0',
                            fontWeight: 500,
                            color: '#374151',
                          }}
                        >
                          Tên đại lý
                        </td>
                        <td
                          style={{
                            padding: '12px 16px',
                            borderBottom: '1px solid #e2e8f0',
                            color: '#1f2937',
                            fontWeight: 600,
                          }}
                        >
                          {selectedUser.name}
                        </td>
                      </tr>
                      <tr>
                        <td
                          style={{
                            padding: '12px 16px',
                            backgroundColor: '#f8fafc',
                            borderBottom: '1px solid #e2e8f0',
                            fontWeight: 500,
                            color: '#374151',
                          }}
                        >
                          Username
                        </td>
                        <td
                          style={{
                            padding: '12px 16px',
                            borderBottom: '1px solid #e2e8f0',
                            color: '#1f2937',
                          }}
                        >
                          {selectedUser.username}
                        </td>
                      </tr>
                      <tr>
                        <td
                          style={{
                            padding: '12px 16px',
                            backgroundColor: '#f8fafc',
                            borderBottom: '1px solid #e2e8f0',
                            fontWeight: 500,
                            color: '#374151',
                          }}
                        >
                          Email
                        </td>
                        <td
                          style={{
                            padding: '12px 16px',
                            borderBottom: '1px solid #e2e8f0',
                            color: '#1f2937',
                          }}
                        >
                          {selectedUser.email}
                        </td>
                      </tr>
                      <tr>
                        <td
                          style={{
                            padding: '12px 16px',
                            backgroundColor: '#f8fafc',
                            borderBottom: '1px solid #e2e8f0',
                            fontWeight: 500,
                            color: '#374151',
                          }}
                        >
                          Số điện thoại
                        </td>
                        <td
                          style={{
                            padding: '12px 16px',
                            borderBottom: '1px solid #e2e8f0',
                            color: '#1f2937',
                          }}
                        >
                          {selectedUser.phone}
                        </td>
                      </tr>
                      <tr>
                        <td
                          style={{
                            padding: '12px 16px',
                            backgroundColor: '#f8fafc',
                            borderBottom: '1px solid #e2e8f0',
                            fontWeight: 500,
                            color: '#374151',
                          }}
                        >
                          Số dư
                        </td>
                        <td
                          style={{
                            padding: '12px 16px',
                            borderBottom: '1px solid #e2e8f0',
                            color: '#059669',
                            fontWeight: 600,
                          }}
                        >
                          {selectedUser.balance
                            ? new Intl.NumberFormat('vi-VN', {
                                style: 'currency',
                                currency: 'VND',
                              }).format(selectedUser.balance)
                            : '0 ₫'}
                        </td>
                      </tr>
                      <tr>
                        <td
                          style={{
                            padding: '12px 16px',
                            backgroundColor: '#f8fafc',
                            borderBottom: '1px solid #e2e8f0',
                            fontWeight: 500,
                            color: '#374151',
                          }}
                        >
                          Vai trò
                        </td>
                        <td
                          style={{
                            padding: '12px 16px',
                            borderBottom: '1px solid #e2e8f0',
                            color: '#1f2937',
                          }}
                        >
                          <Tag color="blue" style={{ margin: 0 }}>
                            {selectedUser.role?.name || 'N/A'}
                          </Tag>
                        </td>
                      </tr>
                      <tr>
                        <td
                          style={{
                            padding: '12px 16px',
                            backgroundColor: '#f8fafc',
                            borderBottom: '1px solid #e2e8f0',
                            fontWeight: 500,
                            color: '#374151',
                          }}
                        >
                          Trạng thái
                        </td>
                        <td
                          style={{
                            padding: '12px 16px',
                            borderBottom: '1px solid #e2e8f0',
                            color: '#1f2937',
                          }}
                        >
                          <Tag
                            color={selectedUser.blocked ? '#ff4d4f' : '#52c41a'}
                            icon={selectedUser.blocked ? <StopOutlined /> : <CheckCircleOutlined />}
                            style={{ margin: 0 }}
                          >
                            {selectedUser.blocked ? 'Đã khóa' : 'Hoạt động'}
                          </Tag>
                        </td>
                      </tr>
                      <tr>
                        <td
                          style={{
                            padding: '12px 16px',
                            backgroundColor: '#f8fafc',
                            fontWeight: 500,
                            color: '#374151',
                          }}
                        >
                          Ngày tạo
                        </td>
                        <td
                          style={{
                            padding: '12px 16px',
                            color: '#1f2937',
                          }}
                        >
                          {new Date(selectedUser.createdAt).toLocaleString('vi-VN')}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </Col>
            </Row>

            {/* Referrer Information */}
            {selectedUser.referUser && (
              <div style={{ marginTop: 24 }}>
                <div style={{ marginBottom: 16 }}>
                  <span style={{ fontWeight: 500, fontSize: 15, color: '#344054' }}>
                    Thông tin người giới thiệu
                  </span>
                </div>
                <div
                  style={{
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    padding: '16px',
                    backgroundColor: '#f8fafc',
                  }}
                >
                  <Row gutter={[16, 8]}>
                    <Col span={6}>
                      <span style={{ fontWeight: 500, color: '#374151' }}>Tên:</span>
                    </Col>
                    <Col span={18}>
                      <span style={{ color: '#1f2937' }}>{selectedUser.referUser.name}</span>
                    </Col>
                    <Col span={6}>
                      <span style={{ fontWeight: 500, color: '#374151' }}>Username:</span>
                    </Col>
                    <Col span={18}>
                      <span style={{ color: '#1f2937' }}>{selectedUser.referUser.username}</span>
                    </Col>
                    <Col span={6}>
                      <span style={{ fontWeight: 500, color: '#374151' }}>Số điện thoại:</span>
                    </Col>
                    <Col span={18}>
                      <span style={{ color: '#1f2937' }}>{selectedUser.referUser.phone}</span>
                    </Col>
                  </Row>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* User Create Modal */}
      <UserCreateModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onSuccess={() => {
          fetchUsers();
          fetchStatistics();
        }}
      />
    </PageContainer>
  );
};

export default UserList;
